#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import tempfile
import shutil
import datetime

def debug_exact_issue():
    """精确复现你的问题"""
    
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import stats, flatten_and_extract_all
    
    print("🔍 精确复现问题:")
    print("=" * 60)
    
    # 1. 重置stats（模拟程序启动）
    print("1. 重置stats状态:")
    stats['files_in_db'] = 0
    print(f"   重置后stats: {stats}")
    print()
    
    # 2. 创建测试环境（模拟W站压缩包解压后的文件）
    print("2. 创建测试环境:")
    temp_dir = tempfile.mkdtemp()
    print(f"   临时目录: {temp_dir}")
    
    # 创建多个测试文件（模拟解压出的文件）
    test_files = []
    for i in range(3):
        test_file = os.path.join(temp_dir, f"sample_{i}.exe")
        with open(test_file, "wb") as f:
            f.write(b"MZ" + f"sample_{i}".encode() * 100)
        test_files.append(test_file)
        print(f"   创建: {os.path.basename(test_file)} ({os.path.getsize(test_file)} bytes)")
    
    print()
    
    # 3. 模拟主程序的统计逻辑
    print("3. 模拟主程序统计逻辑:")
    
    # 记录处理前的统计数据（这是关键步骤）
    before_files_in_db = stats.get('files_in_db', 0)
    print(f"   记录before_files_in_db: {before_files_in_db}")
    
    # 调用flatten_and_extract_all（这会处理文件并更新stats）
    try:
        fake_zip_path = "G:\\z1.精洗\\W站\\test.rar"
        fake_watch_dir = "G:\\z1.精洗\\W站"
        
        print(f"   调用flatten_and_extract_all...")
        flatten_and_extract_all(temp_dir, fake_zip_path, fake_watch_dir, 'W站')
        
        # 检查处理后的状态
        after_files_in_db = stats.get('files_in_db', 0)
        print(f"   处理后files_in_db: {after_files_in_db}")
        
        # 计算写入数量（这是显示在详细信息中的数量）
        main_table_written = after_files_in_db - before_files_in_db
        print(f"   计算的写入数量: {main_table_written}")
        
        print()
        print(f"   🎯 这个数量 {main_table_written} 就是会显示在详细信息中的'写入t_virus_clean_1数量'")
        
    except Exception as e:
        print(f"   ❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # 4. 再次测试（模拟你重复处理同一个压缩包）
    print("4. 再次测试（模拟重复处理）:")
    
    # 再次记录before状态
    before_files_in_db_2 = stats.get('files_in_db', 0)
    print(f"   第二次记录before_files_in_db: {before_files_in_db_2}")
    
    try:
        print(f"   第二次调用flatten_and_extract_all...")
        flatten_and_extract_all(temp_dir, fake_zip_path, fake_watch_dir, 'W站')
        
        after_files_in_db_2 = stats.get('files_in_db', 0)
        print(f"   第二次处理后files_in_db: {after_files_in_db_2}")
        
        main_table_written_2 = after_files_in_db_2 - before_files_in_db_2
        print(f"   第二次计算的写入数量: {main_table_written_2}")
        
        print()
        print(f"   🎯 第二次的数量 {main_table_written_2} 可能就是你看到的0！")
        
    except Exception as e:
        print(f"   ❌ 第二次处理失败: {e}")
    
    print()
    
    # 5. 检查stats变化的详细过程
    print("5. 检查stats变化过程:")
    print(f"   初始值: 0")
    print(f"   第一次处理后: {after_files_in_db}")
    print(f"   第二次处理后: {after_files_in_db_2}")
    print()
    print("   分析:")
    print("   - 如果第一次处理成功，stats['files_in_db']会增加")
    print("   - 如果第二次处理时MD5已存在，stats['files_in_db']不会再增加")
    print("   - 但before_files_in_db是在第二次处理前记录的，所以差值为0")
    
    # 6. 清理
    print()
    print("6. 清理...")
    try:
        shutil.rmtree(temp_dir)
        print("   ✅ 清理完成")
    except Exception as e:
        print(f"   ⚠️  清理失败: {e}")
    
    print()
    print("=" * 60)
    print("🎯 问题分析:")
    print("1. 如果你看到第二次写入数量为0，说明MD5重复检测生效了")
    print("2. 即使你删除了表数据，程序运行期间stats变量会保持状态")
    print("3. 解决方案：重启程序，或者处理不同的文件")
    print()
    print("💡 验证方法:")
    print("1. 重启程序后处理同一个压缩包")
    print("2. 或者修改压缩包中的文件内容，让MD5不同")

if __name__ == "__main__":
    debug_exact_issue()
