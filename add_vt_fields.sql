-- 为主表添加VT文件类型和病毒家族字段
-- 请在MySQL中执行以下SQL语句

-- 添加VT文件类型字段
ALTER TABLE `t_virus_clean_1` 
ADD COLUMN `vt_file_type` VARCHAR(255) DEFAULT '' COMMENT 'VirusTotal文件类型';

-- 添加病毒家族字段  
ALTER TABLE `t_virus_clean_1` 
ADD COLUMN `virus_family` VARCHAR(255) DEFAULT '' COMMENT '病毒家族';

-- 查看表结构确认字段已添加
DESCRIBE `t_virus_clean_1`;

-- 可选：为新字段添加索引以提高查询性能
-- CREATE INDEX idx_vt_file_type ON `t_virus_clean_1`(`vt_file_type`);
-- CREATE INDEX idx_virus_family ON `t_virus_clean_1`(`virus_family`);
