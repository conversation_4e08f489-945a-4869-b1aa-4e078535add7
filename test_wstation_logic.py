#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import tempfile

def test_wstation_logic():
    """测试W站文件夹的特殊判断逻辑"""
    
    # 导入修改后的函数
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import is_executable_file
    
    # 创建测试文件和目录
    test_cases = [
        # W站文件夹中的文件（应该都返回True）
        "W站/test.txt",
        "W站/data.bin", 
        "W站/subfolder/file.dat",
        "some/path/W站/file.unknown",
        "extract/W站/sample.data",
        
        # 非W站文件夹中的文件（按正常逻辑判断）
        "normal/test.txt",
        "data/file.bin",
        "other/folder/file.dat"
    ]
    
    print("测试W站文件夹特殊判断逻辑:")
    print("=" * 60)
    
    # 创建临时目录和文件进行测试
    with tempfile.TemporaryDirectory() as temp_dir:
        for test_path in test_cases:
            # 创建完整路径
            full_path = os.path.join(temp_dir, test_path)
            
            # 创建目录
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            # 创建测试文件（写入一些非可执行内容）
            with open(full_path, 'wb') as f:
                f.write(b'This is test data, not executable')
            
            # 测试判断结果
            result = is_executable_file(full_path)
            
            # 判断是否是W站文件
            is_wstation = '/W站/' in test_path.replace('\\', '/') or test_path.startswith('W站/')
            expected = is_wstation  # W站文件应该返回True，其他返回False（因为不是真正的可执行文件）
            
            status = "✅ 正确" if result == expected else "❌ 错误"
            wstation_mark = "🎯 W站" if is_wstation else "📁 普通"
            
            print(f"{status} | {wstation_mark} | {result} | {test_path}")
    
    print()
    print("说明:")
    print("- 🎯 W站文件夹中的文件应该都返回True")
    print("- 📁 普通文件夹中的非可执行文件应该返回False")

if __name__ == "__main__":
    test_wstation_logic()
