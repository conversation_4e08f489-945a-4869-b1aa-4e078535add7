#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批量更新users_virussample表中的VT信息
从id 4028开始，更新file_type和virus_family字段
"""

import sys
import os
import time
import datetime

# 添加virus_clean模块路径
sys.path.append('virus_clean')

def update_vt_info_batch():
    """批量更新VT信息"""
    print("🔄 批量更新users_virussample表的VT信息")
    print("=" * 60)
    
    try:
        from auto_unzip_and_md5 import (
            query_virustotal, parse_vt_results, DB_CONFIG, QINGBAO_TABLE
        )
        import pymysql
        
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 1. 查询需要更新的记录
        print("1. 查询需要更新的记录:")
        print("-" * 30)
        
        query_sql = f"""
        SELECT id, md5, file_name, file_type, virus_family
        FROM `{QINGBAO_TABLE}`
        WHERE id >= 4028 
        AND (file_type = '未知文件类型' OR file_type = '' OR file_type IS NULL 
             OR virus_family = '' OR virus_family IS NULL)
        ORDER BY id ASC
        """
        
        cursor.execute(query_sql)
        records = cursor.fetchall()
        
        if not records:
            print("   ✅ 没有需要更新的记录")
            cursor.close()
            conn.close()
            return
        
        print(f"   📊 找到 {len(records)} 条需要更新的记录")
        print(f"   📋 ID范围: {records[0][0]} - {records[-1][0]}")
        
        # 2. 批量处理
        print()
        print("2. 开始批量处理:")
        print("-" * 30)
        
        success_count = 0
        error_count = 0
        not_found_count = 0
        rate_limit_count = 0
        
        for i, record in enumerate(records, 1):
            record_id, md5, file_name, current_file_type, current_virus_family = record
            
            print(f"   [{i}/{len(records)}] 处理 ID:{record_id} | {file_name[:30]}...")
            print(f"      MD5: {md5}")
            print(f"      当前文件类型: {current_file_type}")
            print(f"      当前病毒家族: {current_virus_family}")
            
            try:
                # 查询VT
                vt_data = query_virustotal(md5)
                
                if vt_data:
                    # 解析VT结果
                    engine_results, vt_file_type, virus_family = parse_vt_results(md5, vt_data)
                    
                    # 如果获取到新信息，则更新
                    need_update = False
                    update_fields = []
                    update_values = []
                    
                    if vt_file_type and vt_file_type != current_file_type:
                        update_fields.append("file_type = %s")
                        update_values.append(vt_file_type)
                        need_update = True
                    
                    if virus_family and virus_family != current_virus_family:
                        update_fields.append("virus_family = %s")
                        update_values.append(virus_family)
                        need_update = True
                    
                    if need_update:
                        # 执行更新
                        update_sql = f"""
                        UPDATE `{QINGBAO_TABLE}` 
                        SET {', '.join(update_fields)}, updated_at = NOW()
                        WHERE id = %s
                        """
                        update_values.append(record_id)
                        
                        cursor.execute(update_sql, update_values)
                        conn.commit()
                        
                        print(f"      ✅ 更新成功:")
                        if vt_file_type:
                            print(f"         文件类型: {current_file_type} → {vt_file_type}")
                        if virus_family:
                            print(f"         病毒家族: {current_virus_family} → {virus_family}")
                        
                        success_count += 1
                    else:
                        print(f"      ⚠️  VT无新信息，跳过更新")
                        not_found_count += 1
                else:
                    print(f"      ⚠️  VT查询失败或文件未找到")
                    not_found_count += 1
                
                # 避免API限制，每次查询后稍作延迟
                if i % 4 == 0:  # 每4次查询后延迟
                    print(f"      💤 延迟2秒避免API限制...")
                    time.sleep(2)
                
            except Exception as e:
                print(f"      ❌ 处理失败: {e}")
                error_count += 1
                
                # 如果是API限制错误，延迟更长时间
                if "429" in str(e) or "rate" in str(e).lower():
                    rate_limit_count += 1
                    print(f"      ⏰ API限制，延迟60秒...")
                    time.sleep(60)
            
            print()
        
        # 3. 统计结果
        print("=" * 60)
        print("📊 处理结果统计:")
        print(f"   总记录数: {len(records)}")
        print(f"   成功更新: {success_count}")
        print(f"   未找到信息: {not_found_count}")
        print(f"   处理失败: {error_count}")
        print(f"   API限制: {rate_limit_count}")
        
        # 4. 验证更新结果
        print()
        print("4. 验证更新结果:")
        print("-" * 30)
        
        # 查询更新后的统计
        cursor.execute(f"""
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN file_type != '未知文件类型' AND file_type != '' AND file_type IS NOT NULL THEN 1 ELSE 0 END) as has_file_type,
            SUM(CASE WHEN virus_family != '' AND virus_family IS NOT NULL THEN 1 ELSE 0 END) as has_virus_family
        FROM `{QINGBAO_TABLE}`
        WHERE id >= 4028
        """)
        
        result = cursor.fetchone()
        if result:
            total, has_file_type, has_virus_family = result
            print(f"   ID >= 4028 的记录总数: {total}")
            print(f"   有文件类型信息: {has_file_type} ({has_file_type/total*100:.1f}%)")
            print(f"   有病毒家族信息: {has_virus_family} ({has_virus_family/total*100:.1f}%)")
        
        # 显示最近更新的几条记录
        print()
        print("5. 最近更新的记录示例:")
        print("-" * 30)
        
        cursor.execute(f"""
        SELECT id, file_name, file_type, virus_family, updated_at
        FROM `{QINGBAO_TABLE}`
        WHERE id >= 4028 
        AND (file_type != '未知文件类型' AND file_type != '' AND file_type IS NOT NULL)
        ORDER BY updated_at DESC
        LIMIT 5
        """)
        
        updated_records = cursor.fetchall()
        if updated_records:
            for record in updated_records:
                record_id, file_name, file_type, virus_family, updated_at = record
                print(f"   ID:{record_id} | {file_name[:25]:<25} | {file_type:<15} | {virus_family}")
        else:
            print("   暂无更新记录")
        
        cursor.close()
        conn.close()
        
        print()
        print("=" * 60)
        print("🎯 批量更新完成!")
        print("💡 建议:")
        print("- 如果有大量记录需要处理，可以分批运行")
        print("- 注意VT API的使用限制")
        print("- 定期检查更新结果")
        
    except Exception as e:
        print(f"❌ 批量更新失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 确认操作
    print("⚠️  即将批量更新users_virussample表中id >= 4028的记录")
    print("   这将查询VT API获取文件类型和病毒家族信息")
    print("   可能需要较长时间，请确保网络连接稳定")
    print()
    
    confirm = input("确认继续? (y/N): ").strip().lower()
    if confirm in ['y', 'yes']:
        update_vt_info_batch()
    else:
        print("操作已取消")
