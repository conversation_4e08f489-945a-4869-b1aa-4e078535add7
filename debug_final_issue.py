#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import datetime

def debug_final_issue():
    """最终调试：追踪整个统计流程"""
    
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import DB_CONFIG, TABLE_NAME, stats
    import pymysql
    
    print("🔍 最终调试：追踪统计流程")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        
        # 1. 检查当前stats状态
        print("1. 当前程序状态:")
        print(f"   stats = {stats}")
        print(f"   stats['files_in_db'] = {stats.get('files_in_db', 0)}")
        print()
        
        # 2. 检查最近的数据库记录
        print("2. 检查最近的数据库记录:")
        cursor.execute(f"""
            SELECT md5, file_name, get_date, source, `repeat`
            FROM `{TABLE_NAME}` 
            WHERE date = %s 
            ORDER BY id DESC 
            LIMIT 10
        """, (today,))
        recent_records = cursor.fetchall()
        
        if recent_records:
            print(f"   最近10条记录:")
            for i, record in enumerate(recent_records, 1):
                md5, file_name, get_date, source, repeat = record
                status = "重复" if repeat == 1 else "新增"
                print(f"   {i:2d}. {status} | {file_name[:20]:<20} | {get_date} | {source}")
        else:
            print("   没有找到今日记录")
        
        print()
        
        # 3. 检查最近一次处理的时间窗口
        print("3. 分析最近一次处理:")
        if recent_records:
            latest_time = recent_records[0][2]
            print(f"   最新记录时间: {latest_time}")
            
            # 查找同一时间段的所有记录（假设5分钟内为同一批）
            cursor.execute(f"""
                SELECT COUNT(*) as total_count,
                       SUM(CASE WHEN `repeat` = 0 THEN 1 ELSE 0 END) as new_count,
                       SUM(CASE WHEN `repeat` = 1 THEN 1 ELSE 0 END) as repeat_count
                FROM `{TABLE_NAME}` 
                WHERE date = %s 
                AND get_date >= DATE_SUB(%s, INTERVAL 5 MINUTE)
                AND get_date <= DATE_ADD(%s, INTERVAL 5 MINUTE)
            """, (today, latest_time, latest_time))
            
            batch_info = cursor.fetchone()
            if batch_info:
                total_count, new_count, repeat_count = batch_info
                print(f"   同批次记录总数: {total_count}")
                print(f"   新增记录数: {new_count}")
                print(f"   重复记录数: {repeat_count}")
                
                print(f"\n   🎯 理论上'写入t_virus_clean_1数量'应该是: {new_count + repeat_count}")
                print(f"   🎯 但实际显示的是: 0")
        
        print()
        
        # 4. 模拟统计逻辑的问题
        print("4. 分析统计逻辑问题:")
        print("   可能的问题:")
        print("   a) stats['files_in_db']在处理前就已经有值了")
        print("   b) before_files_in_db记录的时机不对")
        print("   c) stats变量被重置了")
        print("   d) 统计逻辑中有其他问题")
        
        print()
        
        # 5. 检查stats重置的影响
        print("5. 检查stats重置逻辑:")
        print("   新版本在每次检测到新压缩包时会执行:")
        print("   stats['files_in_db'] = 0")
        print("   这应该确保每次处理都从0开始")
        print()
        print("   但是如果在记录before_files_in_db之后才重置，")
        print("   那么before_files_in_db可能记录了错误的值")
        
        print()
        
        # 6. 检查情报网表同步情况
        print("6. 检查情报网表同步:")
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM users_virussample 
            WHERE DATE(created_at) = %s
        """, (today,))
        qingbao_count = cursor.fetchone()[0]
        
        main_count = len(recent_records) if recent_records else 0
        print(f"   今日主表记录数: {main_count}")
        print(f"   今日情报网表记录数: {qingbao_count}")
        
        if main_count != qingbao_count:
            print("   ⚠️  主表和情报网表记录数不一致")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    print("=" * 60)
    print("🎯 问题分析:")
    print("1. 如果数据库中有新记录，但统计显示0，说明统计逻辑有问题")
    print("2. 可能的原因:")
    print("   - stats重置的时机不对")
    print("   - before_files_in_db记录的值不正确")
    print("   - 统计计算公式有误")
    print()
    print("💡 建议:")
    print("1. 检查日志中是否有'🔄 重置stats'的消息")
    print("2. 检查是否在正确的时机记录before_files_in_db")
    print("3. 考虑在统计时直接查询数据库而不依赖stats变量")

if __name__ == "__main__":
    debug_final_issue()
