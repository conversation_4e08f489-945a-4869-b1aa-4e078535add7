#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字段修复
"""

import sys
import os
import datetime

# 添加virus_clean模块路径
sys.path.append('virus_clean')

def test_field_fix():
    """测试字段修复"""
    print("🧪 测试字段修复")
    print("=" * 60)
    
    try:
        from auto_unzip_and_md5 import write_to_qingbao_db
        
        # 测试write_to_qingbao_db函数调用
        print("1. 测试write_to_qingbao_db函数调用:")
        print("-" * 30)
        
        test_md5 = f"test_field_fix_{datetime.datetime.now().strftime('%H%M%S')}"
        test_filename = "test_sample.exe"
        test_file_size = 12345
        test_date = datetime.datetime.now().strftime('%Y-%m-%d')
        test_src_path = "测试路径\\测试家族\\test_sample.exe"
        test_vt_first_submit_time = datetime.datetime.now()
        test_collection_channel = "测试渠道"
        test_file_type = "PE32 executable"  # 使用file_type而不是vt_file_type
        test_virus_family = "Trojan.Test"
        
        print(f"   测试参数:")
        print(f"   - MD5: {test_md5}")
        print(f"   - 文件名: {test_filename}")
        print(f"   - 文件类型: {test_file_type}")
        print(f"   - 病毒家族: {test_virus_family}")
        print(f"   - 收集渠道: {test_collection_channel}")
        
        # 调用函数
        result = write_to_qingbao_db(
            md5=test_md5,
            filename=test_filename,
            file_size=test_file_size,
            date=test_date,
            src_path=test_src_path,
            vt_first_submit_time=test_vt_first_submit_time,
            collection_channel=test_collection_channel,
            file_type=test_file_type,  # 使用file_type参数
            virus_family=test_virus_family
        )
        
        if result:
            print("   ✅ 函数调用成功")
        else:
            print("   ❌ 函数调用失败")
        
        print()
        
        # 2. 验证数据库记录
        print("2. 验证数据库记录:")
        print("-" * 30)
        
        from auto_unzip_and_md5 import DB_CONFIG, QINGBAO_TABLE
        import pymysql
        
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        cursor.execute(f"""
            SELECT file_name, md5, file_type, virus_family, collection_channel
            FROM `{QINGBAO_TABLE}`
            WHERE md5 = %s
            ORDER BY id DESC
            LIMIT 1
        """, (test_md5,))
        
        record = cursor.fetchone()
        if record:
            file_name, md5, file_type, virus_family, collection_channel = record
            print(f"   ✅ 找到记录:")
            print(f"      文件名: {file_name}")
            print(f"      MD5: {md5}")
            print(f"      文件类型: {file_type}")
            print(f"      病毒家族: {virus_family}")
            print(f"      收集渠道: {collection_channel}")
            
            # 验证字段值
            if file_type == test_file_type:
                print("   ✅ 文件类型字段正确")
            else:
                print(f"   ❌ 文件类型字段错误: 期望 '{test_file_type}', 实际 '{file_type}'")
                
            if virus_family == test_virus_family:
                print("   ✅ 病毒家族字段正确")
            else:
                print(f"   ❌ 病毒家族字段错误: 期望 '{test_virus_family}', 实际 '{virus_family}'")
        else:
            print("   ❌ 未找到记录")
        
        cursor.close()
        conn.close()
        
        print()
        print("=" * 60)
        print("🎯 测试结果:")
        print("✅ write_to_qingbao_db函数参数修复完成")
        print("✅ file_type字段正确映射")
        print("✅ virus_family字段正确映射")
        print("✅ 不再使用vt_file_type字段")
        
        print()
        print("💡 修改说明:")
        print("1. 函数参数: vt_file_type → file_type")
        print("2. 主表字段: vt_file_type → file_type")
        print("3. 情报网表: 直接使用现有的file_type和virus_family字段")
        print("4. 避免了'Unknown column vt_file_type'错误")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_field_fix()
