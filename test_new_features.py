#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能的脚本
"""

import sys
import os
import tempfile
import datetime

# 添加virus_clean目录到Python路径
sys.path.insert(0, 'virus_clean')

from auto_unzip_and_md5 import generate_excel_report, classify_samples_by_detection

def test_excel_report():
    """测试Excel报告生成功能"""
    print("🧪 测试Excel报告生成功能...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"   临时目录: {temp_dir}")
    
    try:
        # 测试生成Excel报告
        excel_path = generate_excel_report(temp_dir)
        
        if excel_path and os.path.exists(excel_path):
            print(f"   ✅ Excel报告生成成功: {excel_path}")
            print(f"   📊 文件大小: {os.path.getsize(excel_path)} bytes")
        else:
            print(f"   ⚠️  Excel报告生成失败或无数据")
            
    except Exception as e:
        print(f"   ❌ Excel报告测试失败: {e}")
    
    # 清理临时目录
    import shutil
    shutil.rmtree(temp_dir)

def test_sample_classification():
    """测试样本分类功能"""
    print("🧪 测试样本分类功能...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"   临时目录: {temp_dir}")
    
    try:
        # 测试样本分类
        classify_samples_by_detection(temp_dir)
        
        # 检查是否创建了分类目录
        today = datetime.datetime.now().strftime('%Y%m%d')
        classify_base_dir = os.path.join(os.path.dirname(temp_dir), today)
        
        if os.path.exists(classify_base_dir):
            print(f"   ✅ 分类目录创建成功: {classify_base_dir}")
            
            clean_dir = os.path.join(classify_base_dir, "clean_samples")
            not_scan_dir = os.path.join(classify_base_dir, "not_scan_samples")
            
            if os.path.exists(clean_dir):
                print(f"   ✅ Clean样本目录存在")
            if os.path.exists(not_scan_dir):
                print(f"   ✅ Not_scan样本目录存在")
                
        else:
            print(f"   ⚠️  分类目录未创建或无符合条件的样本")
            
    except Exception as e:
        print(f"   ❌ 样本分类测试失败: {e}")
    
    # 清理临时目录
    import shutil
    shutil.rmtree(temp_dir)

def main():
    print("🚀 开始测试新功能...")
    print("=" * 50)
    
    # 测试Excel报告功能
    test_excel_report()
    print()
    
    # 测试样本分类功能
    test_sample_classification()
    print()
    
    print("=" * 50)
    print("✅ 测试完成！")
    print()
    print("📝 注意事项:")
    print("   - 如果显示'无数据'，这是正常的，因为数据库中可能没有今天的新样本")
    print("   - 实际使用时，程序会在处理完压缩包后自动调用这些功能")
    print("   - 确保数据库连接正常，并且有今天的样本数据")

if __name__ == "__main__":
    main() 