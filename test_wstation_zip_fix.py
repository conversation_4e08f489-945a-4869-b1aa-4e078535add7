#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

def test_wstation_zip_logic():
    """测试W站压缩包的处理逻辑"""
    
    print("测试W站压缩包处理逻辑:")
    print("=" * 60)
    
    # 测试zip路径
    test_zip_paths = [
        # W站路径
        "G:\\z1.精洗\\W站\\被步ransom-threatbook-2025.7.25.rar",
        "D:\\extract\\W站\\malware.zip",
        "C:\\temp\\W站\\samples.7z",
        
        # 非W站路径
        "G:\\z1.精洗\\normal\\file.rar",
        "D:\\extract\\other\\malware.zip",
        "C:\\temp\\regular\\samples.7z",
    ]
    
    print("测试zip路径的W站检测:")
    print("-" * 40)
    
    for zip_path in test_zip_paths:
        # 模拟检测逻辑
        is_from_wstation = False
        if zip_path:
            normalized_zip_path = os.path.normpath(zip_path).replace('\\', '/')
            zip_path_parts = normalized_zip_path.split('/')
            if 'W站' in zip_path_parts:
                is_from_wstation = True
        
        status = "🎯 W站" if is_from_wstation else "📁 普通"
        print(f"{status} | {is_from_wstation} | {zip_path}")
    
    print()
    print("修复说明:")
    print("-" * 40)
    print("1. 在flatten_and_extract_all函数中检查原始zip路径")
    print("2. 如果zip路径包含'W站'文件夹，设置is_from_wstation=True")
    print("3. 对于来自W站的文件，即使is_executable_file返回False也继续处理")
    print("4. 这样解决了解压后文件路径不再包含W站信息的问题")
    print()
    print("处理流程:")
    print("原始: G:\\z1.精洗\\W站\\sample.rar")
    print("解压: extract_temp_xxx\\file.exe (路径中没有W站)")
    print("判断: 通过zip路径检测到来自W站，强制处理")

if __name__ == "__main__":
    test_wstation_zip_logic()
