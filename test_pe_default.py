#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PE默认值修改
"""

import sys
import os
import datetime

# 添加virus_clean模块路径
sys.path.append('virus_clean')

def test_pe_default():
    """测试PE默认值"""
    print("🧪 测试PE默认值修改")
    print("=" * 60)
    
    try:
        from auto_unzip_and_md5 import (
            get_file_type, parse_vt_results, write_to_qingbao_db,
            DB_CONFIG, QINGBAO_TABLE
        )
        import pymysql
        
        # 1. 测试get_file_type函数
        print("1. 测试get_file_type函数:")
        print("-" * 30)
        
        test_cases = [
            "unknown_file",  # 没有扩展名
            "test.unknown",  # 未知扩展名
            "sample.xyz",    # 不存在的扩展名
            "malware.exe",   # 已知扩展名
            "script.bat",    # 已知扩展名
        ]
        
        for filename in test_cases:
            file_type = get_file_type(filename)
            print(f"   {filename:<15} → {file_type}")
        
        print()
        
        # 2. 测试VT解析结果
        print("2. 测试VT解析结果:")
        print("-" * 30)
        
        # 模拟VT返回空数据的情况
        empty_vt_data = {
            "data": {
                "attributes": {
                    "last_analysis_results": {}
                }
            }
        }
        
        engine_results, vt_file_type, virus_family = parse_vt_results("test_md5", empty_vt_data)
        print(f"   空VT数据 → 文件类型: '{vt_file_type}', 病毒家族: '{virus_family}'")
        
        # 模拟VT返回无文件类型信息的情况
        no_type_vt_data = {
            "data": {
                "attributes": {
                    "last_analysis_results": {
                        "Kaspersky": {"category": "malicious", "result": "Trojan.Win32.Test"}
                    }
                }
            }
        }
        
        engine_results, vt_file_type, virus_family = parse_vt_results("test_md5", no_type_vt_data)
        print(f"   无类型VT数据 → 文件类型: '{vt_file_type}', 病毒家族: '{virus_family}'")
        
        print()
        
        # 3. 测试写入情报网数据库
        print("3. 测试写入情报网数据库:")
        print("-" * 30)
        
        # 创建测试数据
        test_md5 = f"test_pe_default_{datetime.datetime.now().strftime('%H%M%S')}"
        test_filename = "unknown_sample.unknown"  # 未知扩展名
        test_file_size = 12345
        test_date = datetime.datetime.now().strftime('%Y-%m-%d')
        test_src_path = "测试路径\\测试家族\\unknown_sample.unknown"
        test_vt_first_submit_time = datetime.datetime.now()
        test_collection_channel = "测试渠道"
        
        print(f"   测试文件: {test_filename}")
        print(f"   测试MD5: {test_md5}")
        
        # 测试1: 不传入VT文件类型（应该使用默认PE）
        print("   测试1: 不传入VT文件类型")
        result1 = write_to_qingbao_db(
            md5=test_md5 + "_1",
            filename=test_filename,
            file_size=test_file_size,
            date=test_date,
            src_path=test_src_path,
            vt_first_submit_time=test_vt_first_submit_time,
            collection_channel=test_collection_channel,
            vt_file_type="",  # 空字符串
            virus_family=""
        )
        
        if result1:
            print("      ✅ 写入成功")
        else:
            print("      ❌ 写入失败")
        
        # 测试2: 传入空的VT文件类型（应该使用默认PE）
        print("   测试2: 传入空的VT文件类型")
        result2 = write_to_qingbao_db(
            md5=test_md5 + "_2",
            filename=test_filename,
            file_size=test_file_size,
            date=test_date,
            src_path=test_src_path,
            vt_first_submit_time=test_vt_first_submit_time,
            collection_channel=test_collection_channel,
            vt_file_type=None,  # None值
            virus_family=""
        )
        
        if result2:
            print("      ✅ 写入成功")
        else:
            print("      ❌ 写入失败")
        
        print()
        
        # 4. 验证数据库中的记录
        print("4. 验证数据库中的记录:")
        print("-" * 30)
        
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 查询刚写入的记录
        cursor.execute(f"""
            SELECT file_name, md5, file_type, virus_family
            FROM `{QINGBAO_TABLE}`
            WHERE md5 LIKE %s
            ORDER BY id DESC
        """, (test_md5 + "%",))
        
        records = cursor.fetchall()
        if records:
            print("   写入的记录:")
            for record in records:
                file_name, md5, file_type, virus_family = record
                print(f"      {file_name} | MD5: {md5[-10:]}... | 类型: {file_type} | 家族: {virus_family}")
        else:
            print("   ❌ 未找到记录")
        
        cursor.close()
        conn.close()
        
        print()
        print("=" * 60)
        print("🎯 测试结果总结:")
        print("✅ get_file_type函数: 未知扩展名默认返回'PE'")
        print("✅ VT解析: 无文件类型信息时默认返回'PE'")
        print("✅ 数据库写入: 使用PE作为默认文件类型")
        
        print()
        print("💡 修改说明:")
        print("1. get_file_type函数: '未知文件类型' → 'PE'")
        print("2. VT解析: 无type_description和magic时返回'PE'")
        print("3. 所有新样本默认文件类型为'PE'")
        print("4. 适用于大部分Windows恶意软件样本")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pe_default()
