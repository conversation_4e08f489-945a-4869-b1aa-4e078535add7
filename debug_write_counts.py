#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import datetime

def debug_write_counts():
    """调试写入数量统计问题"""
    
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import DB_CONFIG, TABLE_NAME, QINGBAO_TABLE
    import pymysql
    
    print("调试写入数量统计问题:")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 1. 检查今日主表数据
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        print(f"1. 检查今日主表 {TABLE_NAME} 数据 (日期: {today}):")
        
        cursor.execute(f"SELECT COUNT(*) FROM `{TABLE_NAME}` WHERE date = %s", (today,))
        main_count = cursor.fetchone()[0]
        print(f"   今日主表总记录数: {main_count}")
        
        if main_count > 0:
            # 显示最近几条记录
            cursor.execute(f"""
                SELECT md5, file_name, file_size, src_path, get_date, source 
                FROM `{TABLE_NAME}` 
                WHERE date = %s 
                ORDER BY id DESC 
                LIMIT 5
            """, (today,))
            recent_records = cursor.fetchall()
            
            print("   最近5条记录:")
            for i, record in enumerate(recent_records, 1):
                md5, file_name, file_size, src_path, get_date, source = record
                print(f"   {i}. {file_name} | MD5: {md5[:8]}... | Source: {source}")
        
        print()
        
        # 2. 检查情报网表数据
        print(f"2. 检查情报网表 {QINGBAO_TABLE} 数据:")
        
        cursor.execute(f"SELECT COUNT(*) FROM `{QINGBAO_TABLE}` WHERE DATE(created_at) = %s", (today,))
        qingbao_count = cursor.fetchone()[0]
        print(f"   今日情报网表记录数: {qingbao_count}")
        
        if qingbao_count > 0:
            # 显示最近几条记录
            cursor.execute(f"""
                SELECT md5, file_name, file_size, collection_channel, created_at 
                FROM `{QINGBAO_TABLE}` 
                WHERE DATE(created_at) = %s 
                ORDER BY id DESC 
                LIMIT 5
            """, (today,))
            qingbao_records = cursor.fetchall()
            
            print("   最近5条记录:")
            for i, record in enumerate(qingbao_records, 1):
                md5, file_name, file_size, collection_channel, created_at = record
                print(f"   {i}. {file_name} | MD5: {md5[:8]}... | Channel: {collection_channel}")
        
        print()
        
        # 3. 模拟write_precise_samples_to_qingbao的查询
        print("3. 模拟write_precise_samples_to_qingbao查询:")
        
        cursor.execute(f"""
            SELECT md5, file_name, file_size, src_path, get_date, date, vt_first_submit_time
            FROM `{TABLE_NAME}`
            WHERE date = CURDATE()
            ORDER BY id DESC
        """)
        samples = cursor.fetchall()
        print(f"   查询到的样本数: {len(samples)}")
        
        if samples:
            print("   前3个样本:")
            for i, sample in enumerate(samples[:3], 1):
                md5, filename, file_size, src_path, get_date, date, vt_first_submit_time = sample
                print(f"   {i}. {filename} | MD5: {md5[:8]}... | Date: {date}")
                
                # 检查是否在情报网表中已存在
                cursor.execute(f"SELECT id FROM `{QINGBAO_TABLE}` WHERE md5=%s OR file_name=%s", (md5, filename))
                exists = cursor.fetchone()
                if exists:
                    print(f"      ⚠️  已存在于情报网表中")
                else:
                    print(f"      ✅ 不存在于情报网表中，可以写入")
        
        print()
        
        # 4. 检查时间差异
        print("4. 检查时间相关问题:")
        cursor.execute("SELECT NOW(), CURDATE()")
        now, curdate = cursor.fetchone()
        print(f"   数据库当前时间: {now}")
        print(f"   数据库当前日期: {curdate}")
        print(f"   Python当前日期: {today}")
        
        cursor.close()
        conn.close()
        
        print()
        print("=" * 60)
        print("可能的问题:")
        print("1. 如果主表有数据但情报网表写入数量为0，可能是重复数据问题")
        print("2. 如果查询到的样本数为0，可能是日期查询问题")
        print("3. 如果样本都已存在于情报网表，说明是重复写入")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_write_counts()
