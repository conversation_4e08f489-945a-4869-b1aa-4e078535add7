#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查批量更新的结果
"""

import sys
import os

# 添加virus_clean模块路径
sys.path.append('virus_clean')

def check_update_results():
    """检查更新结果"""
    print("📊 检查批量更新结果")
    print("=" * 60)
    
    try:
        from auto_unzip_and_md5 import DB_CONFIG, QINGBAO_TABLE
        import pymysql
        
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 1. 总体统计
        print("1. 总体统计:")
        print("-" * 30)
        
        cursor.execute(f"""
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN file_type != '未知文件类型' AND file_type != '' AND file_type IS NOT NULL THEN 1 ELSE 0 END) as has_file_type,
            SUM(CASE WHEN virus_family != '' AND virus_family IS NOT NULL THEN 1 ELSE 0 END) as has_virus_family
        FROM `{QINGBAO_TABLE}`
        WHERE id >= 4028
        """)
        
        result = cursor.fetchone()
        if result:
            total, has_file_type, has_virus_family = result
            print(f"   ID >= 4028 的记录总数: {total}")
            print(f"   有文件类型信息: {has_file_type} ({has_file_type/total*100:.1f}%)")
            print(f"   有病毒家族信息: {has_virus_family} ({has_virus_family/total*100:.1f}%)")
        
        print()
        
        # 2. 文件类型分布
        print("2. 文件类型分布:")
        print("-" * 30)
        
        cursor.execute(f"""
        SELECT file_type, COUNT(*) as count
        FROM `{QINGBAO_TABLE}`
        WHERE id >= 4028 AND file_type IS NOT NULL AND file_type != ''
        GROUP BY file_type
        ORDER BY count DESC
        """)
        
        file_types = cursor.fetchall()
        for file_type, count in file_types:
            print(f"   {file_type:<15}: {count} 个")
        
        print()
        
        # 3. 病毒家族分布
        print("3. 病毒家族分布:")
        print("-" * 30)
        
        cursor.execute(f"""
        SELECT virus_family, COUNT(*) as count
        FROM `{QINGBAO_TABLE}`
        WHERE id >= 4028 AND virus_family IS NOT NULL AND virus_family != ''
        GROUP BY virus_family
        ORDER BY count DESC
        LIMIT 10
        """)
        
        virus_families = cursor.fetchall()
        for virus_family, count in virus_families:
            print(f"   {virus_family:<20}: {count} 个")
        
        print()
        
        # 4. 最近更新的记录
        print("4. 最近更新的记录 (前10条):")
        print("-" * 30)
        
        cursor.execute(f"""
        SELECT id, file_name, file_type, virus_family, updated_at
        FROM `{QINGBAO_TABLE}`
        WHERE id >= 4028 
        AND updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY updated_at DESC
        LIMIT 10
        """)
        
        recent_updates = cursor.fetchall()
        if recent_updates:
            for record in recent_updates:
                record_id, file_name, file_type, virus_family, updated_at = record
                print(f"   ID:{record_id} | {file_name[:25]:<25} | {file_type:<12} | {virus_family}")
        else:
            print("   暂无最近更新的记录")
        
        print()
        
        # 5. 仍需更新的记录
        print("5. 仍需更新的记录:")
        print("-" * 30)
        
        cursor.execute(f"""
        SELECT COUNT(*) as count
        FROM `{QINGBAO_TABLE}`
        WHERE id >= 4028 
        AND (file_type = '未知文件类型' OR file_type = '' OR file_type IS NULL)
        """)
        
        still_need_update = cursor.fetchone()[0]
        print(f"   仍需更新文件类型: {still_need_update} 条")
        
        cursor.execute(f"""
        SELECT COUNT(*) as count
        FROM `{QINGBAO_TABLE}`
        WHERE id >= 4028 
        AND (virus_family = '' OR virus_family IS NULL)
        """)
        
        still_need_virus_family = cursor.fetchone()[0]
        print(f"   仍需更新病毒家族: {still_need_virus_family} 条")
        
        cursor.close()
        conn.close()
        
        print()
        print("=" * 60)
        print("🎯 更新效果评估:")
        if has_file_type/total >= 0.8:
            print("✅ 文件类型更新效果良好 (>80%)")
        else:
            print("⚠️  文件类型更新效果一般 (<80%)")
            
        if has_virus_family/total >= 0.7:
            print("✅ 病毒家族更新效果良好 (>70%)")
        else:
            print("⚠️  病毒家族更新效果一般 (<70%)")
        
        print()
        print("💡 后续建议:")
        print("- 定期运行批量更新脚本")
        print("- 对于VT未找到的文件，可以考虑其他检测引擎")
        print("- 监控新增记录的VT信息获取情况")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_update_results()
