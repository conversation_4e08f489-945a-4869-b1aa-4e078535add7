#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import datetime

def debug_stats_tracking():
    """追踪stats变量的变化"""
    
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import DB_CONFIG, TABLE_NAME, stats
    import pymysql
    
    print("追踪stats变量变化:")
    print("=" * 60)
    
    # 1. 检查当前stats状态
    print("1. 当前stats状态:")
    print(f"   stats = {stats}")
    print()
    
    # 2. 检查今日数据库记录
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        
        # 检查今日记录数
        cursor.execute(f"SELECT COUNT(*) FROM `{TABLE_NAME}` WHERE date = %s", (today,))
        today_count = cursor.fetchone()[0]
        
        print(f"2. 今日数据库记录:")
        print(f"   今日总记录数: {today_count}")
        
        # 检查最近的记录时间
        cursor.execute(f"""
            SELECT MAX(get_date), COUNT(*) 
            FROM `{TABLE_NAME}` 
            WHERE date = %s AND get_date >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        """, (today,))
        recent_info = cursor.fetchone()
        
        if recent_info and recent_info[0]:
            latest_time, recent_count = recent_info
            print(f"   最近1小时记录数: {recent_count}")
            print(f"   最新记录时间: {latest_time}")
        
        print()
        
        # 3. 模拟write_to_db的返回值检查
        print("3. 检查write_to_db可能的返回情况:")
        
        # 检查最近几条记录的MD5是否重复
        cursor.execute(f"""
            SELECT md5, file_name, `repeat`, get_date
            FROM `{TABLE_NAME}` 
            WHERE date = %s 
            ORDER BY id DESC 
            LIMIT 5
        """, (today,))
        recent_records = cursor.fetchall()
        
        if recent_records:
            print("   最近5条记录的状态:")
            for i, record in enumerate(recent_records, 1):
                md5, file_name, repeat, get_date = record
                
                # 检查这个MD5在主表中的重复情况
                cursor.execute(f"SELECT COUNT(*) FROM `{TABLE_NAME}` WHERE md5=%s", (md5,))
                main_count = cursor.fetchone()[0]
                
                # 检查这个MD5在大库中的情况
                cursor.execute("SELECT COUNT(*) FROM `t_hash_all` WHERE md5=%s", (md5,))
                big_count = cursor.fetchone()[0]
                
                status = "重复" if repeat == 1 else "新增"
                main_status = f"主表{main_count}次" if main_count > 1 else "主表首次"
                big_status = f"大库存在" if big_count > 0 else "大库不存在"
                
                print(f"   {i}. {file_name[:20]:<20} | {status} | {main_status} | {big_status}")
                print(f"      MD5: {md5[:16]}... | 时间: {get_date}")
                
                # 分析write_to_db的返回值
                if main_count > 1:
                    expected_return = "None (MD5在主表中重复)"
                elif big_count > 0:
                    expected_return = "True (大库重复，写入repeat=1)"
                else:
                    expected_return = "True (新MD5，正常写入)"
                
                print(f"      预期write_to_db返回: {expected_return}")
                print()
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
    
    print("=" * 60)
    print("分析结论:")
    print("1. 如果write_to_db返回True，stats['files_in_db']应该+1")
    print("2. 如果write_to_db返回None，stats['files_in_db']不变")
    print("3. 如果所有文件都返回None（MD5重复），则统计为0")
    print()
    print("可能的问题:")
    print("- 所有MD5都在主表中重复了")
    print("- stats变量作用域问题")
    print("- 统计逻辑时机问题")

if __name__ == "__main__":
    debug_stats_tracking()
