2025-07-25 11:56:53,769 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpu__5uvd8\W站/test.txt
2025-07-25 11:56:53,771 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpu__5uvd8\W站/data.bin
2025-07-25 11:56:53,773 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpu__5uvd8\W站/subfolder/file.dat
2025-07-25 11:56:53,775 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpu__5uvd8\some/path/W站/file.unknown
2025-07-25 11:56:53,778 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpu__5uvd8\extract/W站/sample.data
2025-07-25 13:16:21,774 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpfc7wij8c\W站/test.txt
2025-07-25 13:16:21,776 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpfc7wij8c\W站/subfolder/file.dat
2025-07-25 13:16:21,777 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpfc7wij8c\some/path/W站/file.unknown
2025-07-25 13:16:21,780 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpfc7wij8c\extract/W站/sample.data
2025-07-25 13:23:08,235 INFO 🔍 开始将mredr报毒的样本写入大库t_hash_all...
2025-07-25 13:23:08,282 DEBUG ✅ 写入大库: 07c04422dfcef097fa5cd8d0548e0c98e5ab694854ef076546f5a6d3f17b747f | MD5: b31980b8c9deb631656cb65313d15758 | MREDR: Ransom/LockBit_f | ID: 750091
2025-07-25 13:23:08,293 DEBUG ✅ 写入大库: 1257bd5dbb7a42a46399d740e882f7567e834565d5cc3eba5d89e5892ac95240 | MD5: 28b84fdfe9643c6596cd38bbf006bb6b | MREDR: HVM_Ransom/LockFile_b | ID: 750092
2025-07-25 13:23:08,302 DEBUG ✅ 写入大库: 2917e648b8d55c8bbfe6df7ca0d310e102b9fa4fe0425b2f229621e88eb9c37a | MD5: aed4e581ed9c38716b8a1b080023bb42 | MREDR: HVM_Ransom/LockFile_b | ID: 750093
2025-07-25 13:23:08,312 DEBUG ✅ 写入大库: 3ddcb96a68522610e718c6ca5de42a7d4c88bf5cfba5fc0a30eeffb8f9a571ce | MD5: aa2f1d73b19ae753a3f1a7a6981f9d5b | MREDR: ti!3DDCB96A6852 | ID: 750094
2025-07-25 13:23:08,319 DEBUG ✅ 写入大库: 54c29124b2171053d5399a99706a3d5a20a3b06da31d6225c2e0fce7e9845bf3 | MD5: 14289345a64711afe51dd4ddbb512db6 | MREDR: Ransom/MegaCortex_a | ID: 750095
2025-07-25 13:23:08,328 DEBUG ✅ 写入大库: 56dfe55b016c08f09dd5a2ab58504b377a3cd66ffba236a5a0539f6e2e39aa71 | MD5: 027edad8db0e1abe6e88d073a9eb296a | MREDR: Ransom/GenaLocker_a | ID: 750096
2025-07-25 13:23:08,344 DEBUG ✅ 写入大库: 635f8e19b199652ba6aca8d5b24e6f30b6d3c74b8ff7af423f979adf2dc7d58c | MD5: 2cecb0b5147c8b4de31eea52f3ea7e59 | MREDR: Ransom/Akira_c | ID: 750097
2025-07-25 13:23:08,352 DEBUG ✅ 写入大库: 82c28ec468d936eb34eb7c7ffed5f3d1ca2c57e0424024e3c8faa370690d9883 | MD5: 7876c000e6956018e64ca3e18e60ae55 | MREDR: Ransom/LockBit_f | ID: 750098
2025-07-25 13:23:08,363 DEBUG ✅ 写入大库: cf43b4f418ca769bc40b8a2381b2893f2deec8208859a83661589e86d3bda9d9 | MD5: 33745d14016a7e858ba8c92344bbf059 | MREDR: VHO_Trojan-Ransom_Win32_GandCrypt_gen | ID: 750099
2025-07-25 13:23:08,374 DEBUG ✅ 写入大库: dc22a6ccc6866ec10032d9a3780184ce28dd31de0b193a7fb2932225fc381733 | MD5: 32e6a0a5d2a29cde73f737c7c539c4aa | MREDR: HEUR_Trojan_Win32_Generic | ID: 750100
2025-07-25 13:23:08,396 DEBUG ✅ 写入大库: ea134ca77412f46796572f1c3e055d3c8de7d0053ad01740d03b672e253d0b71 | MD5: eb5477e72d59d6b5bc9932664cc0e968 | MREDR: VHO_Trojan-Ransom_Win32_GandCrypt_gen | ID: 750101
2025-07-25 13:23:08,409 INFO ✅ 大库写入完成: 共写入 11 个mredr报毒样本
2025-07-25 13:23:08,409 INFO 🔍 开始将mredr报ransom的样本写入小库t_hash_ransom...
2025-07-25 13:23:08,478 DEBUG ✅ 写入小库: 07c04422dfcef097fa5cd8d0548e0c98e5ab694854ef076546f5a6d3f17b747f | MD5: b31980b8c9deb631656cb65313d15758 | MREDR: Ransom/LockBit_f | ID: 15683
2025-07-25 13:23:08,492 DEBUG ✅ 写入小库: 1257bd5dbb7a42a46399d740e882f7567e834565d5cc3eba5d89e5892ac95240 | MD5: 28b84fdfe9643c6596cd38bbf006bb6b | MREDR: HVM_Ransom/LockFile_b | ID: 15684
2025-07-25 13:23:08,505 DEBUG ✅ 写入小库: 2917e648b8d55c8bbfe6df7ca0d310e102b9fa4fe0425b2f229621e88eb9c37a | MD5: aed4e581ed9c38716b8a1b080023bb42 | MREDR: HVM_Ransom/LockFile_b | ID: 15685
2025-07-25 13:23:08,519 DEBUG ✅ 写入小库: 54c29124b2171053d5399a99706a3d5a20a3b06da31d6225c2e0fce7e9845bf3 | MD5: 14289345a64711afe51dd4ddbb512db6 | MREDR: Ransom/MegaCortex_a | ID: 15686
2025-07-25 13:23:08,548 DEBUG ✅ 写入小库: 56dfe55b016c08f09dd5a2ab58504b377a3cd66ffba236a5a0539f6e2e39aa71 | MD5: 027edad8db0e1abe6e88d073a9eb296a | MREDR: Ransom/GenaLocker_a | ID: 15687
2025-07-25 13:23:08,562 DEBUG ✅ 写入小库: 635f8e19b199652ba6aca8d5b24e6f30b6d3c74b8ff7af423f979adf2dc7d58c | MD5: 2cecb0b5147c8b4de31eea52f3ea7e59 | MREDR: Ransom/Akira_c | ID: 15688
2025-07-25 13:23:08,578 DEBUG ✅ 写入小库: 82c28ec468d936eb34eb7c7ffed5f3d1ca2c57e0424024e3c8faa370690d9883 | MD5: 7876c000e6956018e64ca3e18e60ae55 | MREDR: Ransom/LockBit_f | ID: 15689
2025-07-25 13:23:08,592 DEBUG ✅ 写入小库: cf43b4f418ca769bc40b8a2381b2893f2deec8208859a83661589e86d3bda9d9 | MD5: 33745d14016a7e858ba8c92344bbf059 | MREDR: VHO_Trojan-Ransom_Win32_GandCrypt_gen | ID: 15690
2025-07-25 13:23:08,611 DEBUG ✅ 写入小库: ea134ca77412f46796572f1c3e055d3c8de7d0053ad01740d03b672e253d0b71 | MD5: eb5477e72d59d6b5bc9932664cc0e968 | MREDR: VHO_Trojan-Ransom_Win32_GandCrypt_gen | ID: 15691
2025-07-25 13:23:08,615 INFO ✅ 小库写入完成: 共写入 9 个mredr报ransom样本
2025-07-25 13:23:08,618 INFO 📁 开始分类样本文件...
2025-07-25 13:23:08,620 INFO 📁 创建分类目录: C:\Users\<USER>\AppData\Local\Temp\20250725
2025-07-25 13:23:08,620 INFO    - Clean样本目录: C:\Users\<USER>\AppData\Local\Temp\20250725\clean_samples
2025-07-25 13:23:08,620 INFO    - Not_scan样本目录: C:\Users\<USER>\AppData\Local\Temp\20250725\not_scan_samples
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: 07c04422dfcef097fa5cd8d0548e0c98e5ab694854ef076546f5a6d3f17b747f
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: 1257bd5dbb7a42a46399d740e882f7567e834565d5cc3eba5d89e5892ac95240
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: 2917e648b8d55c8bbfe6df7ca0d310e102b9fa4fe0425b2f229621e88eb9c37a
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: 3ddcb96a68522610e718c6ca5de42a7d4c88bf5cfba5fc0a30eeffb8f9a571ce
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: 54c29124b2171053d5399a99706a3d5a20a3b06da31d6225c2e0fce7e9845bf3
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: 56dfe55b016c08f09dd5a2ab58504b377a3cd66ffba236a5a0539f6e2e39aa71
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: 635f8e19b199652ba6aca8d5b24e6f30b6d3c74b8ff7af423f979adf2dc7d58c
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: 82c28ec468d936eb34eb7c7ffed5f3d1ca2c57e0424024e3c8faa370690d9883
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: 877174e46e6528d97f7e27bf03c7ebda73a2c9ba6a22c6e9210deec5293a8326
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: c9d658353dd7d1116956d4c77c20ecfe09e808a396d8a8dee972e104b73aff38
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: cf43b4f418ca769bc40b8a2381b2893f2deec8208859a83661589e86d3bda9d9
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: dc22a6ccc6866ec10032d9a3780184ce28dd31de0b193a7fb2932225fc381733
2025-07-25 13:23:08,648 DEBUG ⚠️  文件不存在，跳过: ea134ca77412f46796572f1c3e055d3c8de7d0053ad01740d03b672e253d0b71
2025-07-25 13:23:08,648 INFO ✅ 样本分类完成:
2025-07-25 13:23:08,648 INFO    - Clean样本: 0 个
2025-07-25 13:23:08,648 INFO    - Not_scan样本: 0 个 (6个以上引擎报not_scan且mredr为clean)
2025-07-25 13:23:08,648 INFO    - 分类目录: C:\Users\<USER>\AppData\Local\Temp\20250725
2025-07-25 13:23:08,686 INFO 📝 今日精确目录暂无新样本需要写入情报网
2025-07-25 13:34:32,964 DEBUG 🔢 计算MD5: pe_file.exe
2025-07-25 13:34:32,965 DEBUG ✅ MD5计算完成: pe_file.exe -> ead3d4cba62cad943dca9fa88139d258
2025-07-25 13:34:32,966 DEBUG 🔢 计算MD5: script_file.bat
2025-07-25 13:34:32,966 DEBUG ✅ MD5计算完成: script_file.bat -> 30986fcf6df3f202fdeb4249048c5ef5
2025-07-25 13:34:32,967 DEBUG 🔢 计算MD5: text_file.txt
2025-07-25 13:34:32,967 DEBUG ✅ MD5计算完成: text_file.txt -> bdc70a6c91dbb9b87a3bde0436bfd3f0
2025-07-25 13:34:32,969 DEBUG 🔢 计算MD5: data_file.bin
2025-07-25 13:34:32,969 DEBUG ✅ MD5计算完成: data_file.bin -> d15ae53931880fd7b724dd7888b4b4ed
2025-07-25 13:34:32,970 DEBUG 🔢 计算MD5: w_station_file
2025-07-25 13:34:32,970 DEBUG ✅ MD5计算完成: w_station_file -> 9465879efaebb096dbbf6bf78a15940c
2025-07-25 13:34:32,971 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpelfreflw\W站\w_station_file
2025-07-25 13:36:54,867 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpo_x65w_3\W站\test.txt
2025-07-25 13:36:54,868 DEBUG 🔢 计算MD5: test.txt
2025-07-25 13:36:54,868 DEBUG ✅ MD5计算完成: test.txt -> 00ef84e6c671daddc8ff130421c06a29
2025-07-25 13:36:54,870 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpo_x65w_3\W站\data.bin
2025-07-25 13:36:54,870 DEBUG 🔢 计算MD5: data.bin
2025-07-25 13:36:54,871 DEBUG ✅ MD5计算完成: data.bin -> d15ae53931880fd7b724dd7888b4b4ed
2025-07-25 13:36:54,873 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpo_x65w_3\W站\sample_file
2025-07-25 13:36:54,874 DEBUG 🔢 计算MD5: sample_file
2025-07-25 13:36:54,874 DEBUG ✅ MD5计算完成: sample_file -> ac818b4b403371c6bd400ae64d09cb66
2025-07-25 13:36:54,876 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpo_x65w_3\W站\malware.exe
2025-07-25 13:36:54,876 DEBUG 🔢 计算MD5: malware.exe
2025-07-25 13:36:54,876 DEBUG ✅ MD5计算完成: malware.exe -> ead3d4cba62cad943dca9fa88139d258
2025-07-25 13:41:29,915 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpet7ro0x0\z1.精洗\W站\sample1.txt
2025-07-25 13:41:29,916 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpet7ro0x0\z1.精洗\W站\subfolder\sample2.bin
2025-07-25 13:41:29,918 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpet7ro0x0\extract\W站\malware.dat
2025-07-25 13:41:29,919 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpet7ro0x0\W站\file.txt
2025-07-25 13:41:29,921 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpet7ro0x0\some\path\W站\file.bin
2025-07-25 13:41:29,923 INFO 🎯 W站文件夹文件，直接判定为可执行: C:\Users\<USER>\AppData\Local\Temp\tmpet7ro0x0\extract\20250725\W站\sample.exe
2025-07-25 15:03:43,603 INFO [DEBUG] flatten_and_extract_all: 递归解压前 C:\Users\<USER>\AppData\Local\Temp\tmph7_ivm9g 文件列表: ['test_sample.exe']
2025-07-25 15:03:43,606 INFO [DEBUG] flatten_and_extract_all: 递归解压后 C:\Users\<USER>\AppData\Local\Temp\tmph7_ivm9g 文件列表: ['test_sample.exe']
2025-07-25 15:03:43,606 INFO 🎯 检测到来自W站文件夹的压缩包: G:\z1.精洗\W站\test.rar
2025-07-25 15:03:43,607 INFO [DEBUG] flatten_and_extract_all: 扁平化后 C:\Users\<USER>\AppData\Local\Temp\tmph7_ivm9g 文件列表: ['test_sample.exe']
2025-07-25 15:03:43,607 DEBUG 🔢 计算MD5: test_sample.exe
2025-07-25 15:03:43,607 DEBUG ✅ MD5计算完成: test_sample.exe -> 380f243d39728ac68f38983c31759dff
2025-07-25 15:03:43,607 INFO [DEBUG] write_to_db: C:\Users\<USER>\AppData\Local\Temp\tmph7_ivm9g\test_sample.exe md5=380f243d39728ac68f38983c31759dff
2025-07-25 15:03:43,607 DEBUG 💾 写入数据库: test_sample
2025-07-25 15:03:43,630 DEBUG 🔍 检查大库是否存在相同MD5: 380f243d39728ac68f38983c31759dff
2025-07-25 15:03:43,633 INFO 🔍 大库中未发现MD5，开始VT查询: test_sample | MD5: 380f243d39728ac68f38983c31759dff
2025-07-25 15:03:43,633 DEBUG 🔍 VT查询: 380f243d39728ac68f38983c31759dff (API #1)
2025-07-25 15:03:43,635 DEBUG Starting new HTTPS connection (1): www.virustotal.com:443
2025-07-25 15:03:45,309 DEBUG https://www.virustotal.com:443 "GET /api/v3/files/380f243d39728ac68f38983c31759dff HTTP/1.1" 200 None
2025-07-25 15:03:45,309 INFO    ✅ VT查询成功 (API #1)
2025-07-25 15:03:45,354 INFO ✅ 样本 380f243d39728ac68f38983c31759dff 已写入情报网数据库 (序号: 2556, 类型: 未知文件类型)
2025-07-25 15:03:45,355 INFO [DEBUG] flatten_and_extract_all: 递归解压前 C:\Users\<USER>\AppData\Local\Temp\tmph7_ivm9g 文件列表: ['test_sample.exe']
2025-07-25 15:03:45,356 INFO [DEBUG] flatten_and_extract_all: 递归解压后 C:\Users\<USER>\AppData\Local\Temp\tmph7_ivm9g 文件列表: ['test_sample.exe']
2025-07-25 15:03:45,357 INFO 🎯 检测到来自W站文件夹的压缩包: G:\z1.精洗\W站\test.rar
2025-07-25 15:03:45,357 INFO [DEBUG] flatten_and_extract_all: 扁平化后 C:\Users\<USER>\AppData\Local\Temp\tmph7_ivm9g 文件列表: ['test_sample.exe']
2025-07-25 15:03:45,358 DEBUG 🔢 计算MD5: test_sample.exe
2025-07-25 15:03:45,358 DEBUG ✅ MD5计算完成: test_sample.exe -> 380f243d39728ac68f38983c31759dff
2025-07-25 15:03:45,358 INFO [DEBUG] write_to_db: C:\Users\<USER>\AppData\Local\Temp\tmph7_ivm9g\test_sample.exe md5=380f243d39728ac68f38983c31759dff
2025-07-25 15:03:45,358 DEBUG 💾 写入数据库: test_sample
2025-07-25 15:03:45,373 INFO ⚠️ MD5已存在，跳过写入: test_sample | MD5: 380f243d39728ac68f38983c31759dff
2025-07-25 15:10:08,801 INFO [DEBUG] flatten_and_extract_all: 递归解压前 C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l 文件列表: ['sample_0.exe', 'sample_1.exe', 'sample_2.exe']
2025-07-25 15:10:08,802 INFO [DEBUG] flatten_and_extract_all: 递归解压后 C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l 文件列表: ['sample_0.exe', 'sample_1.exe', 'sample_2.exe']
2025-07-25 15:10:08,802 INFO 🎯 检测到来自W站文件夹的压缩包: G:\z1.精洗\W站\test.rar
2025-07-25 15:10:08,802 INFO [DEBUG] flatten_and_extract_all: 扁平化后 C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l 文件列表: ['sample_0.exe', 'sample_1.exe', 'sample_2.exe']
2025-07-25 15:10:08,803 DEBUG 🔢 计算MD5: sample_0.exe
2025-07-25 15:10:08,803 DEBUG ✅ MD5计算完成: sample_0.exe -> d1ce1e6596587ac9a51e2df174515ef1
2025-07-25 15:10:08,803 INFO [DEBUG] write_to_db: C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l\sample_0.exe md5=d1ce1e6596587ac9a51e2df174515ef1
2025-07-25 15:10:08,803 DEBUG 💾 写入数据库: sample_0
2025-07-25 15:10:08,822 DEBUG 🔍 检查大库是否存在相同MD5: d1ce1e6596587ac9a51e2df174515ef1
2025-07-25 15:10:08,825 INFO 🔍 大库中未发现MD5，开始VT查询: sample_0 | MD5: d1ce1e6596587ac9a51e2df174515ef1
2025-07-25 15:10:08,826 DEBUG 🔍 VT查询: d1ce1e6596587ac9a51e2df174515ef1 (API #1)
2025-07-25 15:10:08,827 DEBUG Starting new HTTPS connection (1): www.virustotal.com:443
2025-07-25 15:10:12,192 DEBUG https://www.virustotal.com:443 "GET /api/v3/files/d1ce1e6596587ac9a51e2df174515ef1 HTTP/1.1" 404 None
2025-07-25 15:10:12,193 INFO    ⚠️  VT文件未找到: d1ce1e6596587ac9a51e2df174515ef1
2025-07-25 15:10:12,249 INFO ✅ 样本 d1ce1e6596587ac9a51e2df174515ef1 已写入情报网数据库 (序号: 2557, 类型: 未知文件类型)
2025-07-25 15:10:12,250 DEBUG 🔢 计算MD5: sample_1.exe
2025-07-25 15:10:12,250 DEBUG ✅ MD5计算完成: sample_1.exe -> f9d1b56760266aa55188c9e525e5be5c
2025-07-25 15:10:12,250 INFO [DEBUG] write_to_db: C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l\sample_1.exe md5=f9d1b56760266aa55188c9e525e5be5c
2025-07-25 15:10:12,250 DEBUG 💾 写入数据库: sample_1
2025-07-25 15:10:12,285 DEBUG 🔍 检查大库是否存在相同MD5: f9d1b56760266aa55188c9e525e5be5c
2025-07-25 15:10:12,286 INFO 🔍 大库中未发现MD5，开始VT查询: sample_1 | MD5: f9d1b56760266aa55188c9e525e5be5c
2025-07-25 15:10:12,287 DEBUG 🔍 VT查询: f9d1b56760266aa55188c9e525e5be5c (API #2)
2025-07-25 15:10:12,763 DEBUG https://www.virustotal.com:443 "GET /api/v3/files/f9d1b56760266aa55188c9e525e5be5c HTTP/1.1" 404 None
2025-07-25 15:10:12,763 INFO    ⚠️  VT文件未找到: f9d1b56760266aa55188c9e525e5be5c
2025-07-25 15:10:12,805 INFO ✅ 样本 f9d1b56760266aa55188c9e525e5be5c 已写入情报网数据库 (序号: 2558, 类型: 未知文件类型)
2025-07-25 15:10:12,806 DEBUG 🔢 计算MD5: sample_2.exe
2025-07-25 15:10:12,806 DEBUG ✅ MD5计算完成: sample_2.exe -> af8ec336abadcd740e29d32d109f02ca
2025-07-25 15:10:12,806 INFO [DEBUG] write_to_db: C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l\sample_2.exe md5=af8ec336abadcd740e29d32d109f02ca
2025-07-25 15:10:12,806 DEBUG 💾 写入数据库: sample_2
2025-07-25 15:10:12,841 DEBUG 🔍 检查大库是否存在相同MD5: af8ec336abadcd740e29d32d109f02ca
2025-07-25 15:10:12,844 INFO 🔍 大库中未发现MD5，开始VT查询: sample_2 | MD5: af8ec336abadcd740e29d32d109f02ca
2025-07-25 15:10:12,844 DEBUG 🔍 VT查询: af8ec336abadcd740e29d32d109f02ca (API #3)
2025-07-25 15:10:13,529 DEBUG https://www.virustotal.com:443 "GET /api/v3/files/af8ec336abadcd740e29d32d109f02ca HTTP/1.1" 404 None
2025-07-25 15:10:13,530 INFO    ⚠️  VT文件未找到: af8ec336abadcd740e29d32d109f02ca
2025-07-25 15:10:13,594 INFO ✅ 样本 af8ec336abadcd740e29d32d109f02ca 已写入情报网数据库 (序号: 2559, 类型: 未知文件类型)
2025-07-25 15:10:13,595 INFO [DEBUG] flatten_and_extract_all: 递归解压前 C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l 文件列表: ['sample_0.exe', 'sample_1.exe', 'sample_2.exe']
2025-07-25 15:10:13,596 INFO [DEBUG] flatten_and_extract_all: 递归解压后 C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l 文件列表: ['sample_0.exe', 'sample_1.exe', 'sample_2.exe']
2025-07-25 15:10:13,597 INFO 🎯 检测到来自W站文件夹的压缩包: G:\z1.精洗\W站\test.rar
2025-07-25 15:10:13,597 INFO [DEBUG] flatten_and_extract_all: 扁平化后 C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l 文件列表: ['sample_0.exe', 'sample_1.exe', 'sample_2.exe']
2025-07-25 15:10:13,598 DEBUG 🔢 计算MD5: sample_0.exe
2025-07-25 15:10:13,598 DEBUG ✅ MD5计算完成: sample_0.exe -> d1ce1e6596587ac9a51e2df174515ef1
2025-07-25 15:10:13,598 INFO [DEBUG] write_to_db: C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l\sample_0.exe md5=d1ce1e6596587ac9a51e2df174515ef1
2025-07-25 15:10:13,598 DEBUG 💾 写入数据库: sample_0
2025-07-25 15:10:13,632 INFO ⚠️ MD5已存在，跳过写入: sample_0 | MD5: d1ce1e6596587ac9a51e2df174515ef1
2025-07-25 15:10:13,633 DEBUG 🔢 计算MD5: sample_1.exe
2025-07-25 15:10:13,633 DEBUG ✅ MD5计算完成: sample_1.exe -> f9d1b56760266aa55188c9e525e5be5c
2025-07-25 15:10:13,633 INFO [DEBUG] write_to_db: C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l\sample_1.exe md5=f9d1b56760266aa55188c9e525e5be5c
2025-07-25 15:10:13,634 DEBUG 💾 写入数据库: sample_1
2025-07-25 15:10:13,664 INFO ⚠️ MD5已存在，跳过写入: sample_1 | MD5: f9d1b56760266aa55188c9e525e5be5c
2025-07-25 15:10:13,664 DEBUG 🔢 计算MD5: sample_2.exe
2025-07-25 15:10:13,665 DEBUG ✅ MD5计算完成: sample_2.exe -> af8ec336abadcd740e29d32d109f02ca
2025-07-25 15:10:13,665 INFO [DEBUG] write_to_db: C:\Users\<USER>\AppData\Local\Temp\tmptzl5lq8l\sample_2.exe md5=af8ec336abadcd740e29d32d109f02ca
2025-07-25 15:10:13,665 DEBUG 💾 写入数据库: sample_2
2025-07-25 15:10:13,681 INFO ⚠️ MD5已存在，跳过写入: sample_2 | MD5: af8ec336abadcd740e29d32d109f02ca
2025-08-06 09:44:06,319 DEBUG 🔍 VT查询: 44d88612fea8a8f36de82e1278abb02f (API #1)
2025-08-06 09:44:06,321 DEBUG Starting new HTTPS connection (1): www.virustotal.com:443
2025-08-06 09:44:11,823 DEBUG https://www.virustotal.com:443 "GET /api/v3/files/44d88612fea8a8f36de82e1278abb02f HTTP/1.1" 200 None
2025-08-06 09:44:16,283 INFO    ✅ VT查询成功 (API #1)
2025-08-06 09:44:16,283 DEBUG VT解析结果 - 文件类型: Powershell, 病毒家族: Virus
2025-08-06 09:44:16,340 INFO ✅ 样本 44d88612fea8a8f36de82e1278abb02f 已写入情报网数据库 (序号: 2590, 类型: Powershell)
2025-08-06 09:57:12,895 INFO ✅ 样本 test095712abc123def456 已写入情报网数据库 (序号: 2591, 类型: PE32 executable)
2025-08-06 09:57:12,933 DEBUG 📝 样本 different_name_095712.exe (MD5: test095712abc123def456) 已存在于情报网数据库，跳过
2025-08-06 09:57:12,934 DEBUG 🔍 VT查询: 5d41402abc4b2a76b9719d911017c592 (API #1)
2025-08-06 09:57:12,937 DEBUG Starting new HTTPS connection (1): www.virustotal.com:443
2025-08-06 09:57:14,875 DEBUG https://www.virustotal.com:443 "GET /api/v3/files/5d41402abc4b2a76b9719d911017c592 HTTP/1.1" 200 None
2025-08-06 09:57:14,875 INFO    ✅ VT查询成功 (API #1)
2025-08-06 09:57:14,875 DEBUG VT解析结果 - 文件类型: Text, 病毒家族: 
2025-08-06 09:58:07,328 INFO ✅ 样本 test095807abc123def456 已写入情报网数据库 (序号: 2592, 类型: PE32 executable)
2025-08-06 09:58:07,392 DEBUG 📝 样本 different_name_095807.exe (MD5: test095807abc123def456) 已存在于情报网数据库，跳过
2025-08-06 09:58:07,392 DEBUG 🔍 VT查询: 5d41402abc4b2a76b9719d911017c592 (API #1)
2025-08-06 09:58:07,394 DEBUG Starting new HTTPS connection (1): www.virustotal.com:443
2025-08-06 09:58:08,939 DEBUG https://www.virustotal.com:443 "GET /api/v3/files/5d41402abc4b2a76b9719d911017c592 HTTP/1.1" 200 None
2025-08-06 09:58:08,940 INFO    ✅ VT查询成功 (API #1)
2025-08-06 09:58:08,940 DEBUG VT解析结果 - 文件类型: Text, 病毒家族: 
