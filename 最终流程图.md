# 最终程序流程图（批量写入情报网）

## 主程序流程（最终版）

```mermaid
flowchart TD
    A[程序启动] --> B[初始化数据库连接]
    B --> C[设置日志系统]
    C --> D[加载VirusTotal API密钥]
    D --> E[配置双目录监控]
    E --> F[扫描精确监控目录]
    F --> G[递归搜索子文件夹]
    G --> H[扫描非精确监控目录]
    H --> I[递归搜索子文件夹]
    I --> J[合并发现的压缩包]
    
    J --> K{发现新压缩包?}
    K -->|否| L[等待60秒]
    L --> F
    K -->|是| M[解压压缩包到临时目录]
    
    M --> N{解压成功?}
    N -->|否| O[记录解压错误]
    O --> L
    N -->|是| P[使用flatten_and_extract_all处理文件]
    
    P --> Q[计算MD5并写入主数据库]
    Q --> R[生成Excel报告]
    R --> S[分类样本到对应文件夹]
    S --> T[写入恶意软件数据库]
    T --> U[写入勒索软件数据库]
    U --> V{是精确目录?}
    V -->|是| W[批量写入情报网数据库]
    V -->|否| X[更新处理统计]
    W --> X
    X --> L
```

## 文件处理流程（最终版）

```mermaid
flowchart TD
    A[开始处理解压文件] --> B[遍历解压目录]
    B --> C[获取文件路径]
    C --> D[计算相对路径]
    D --> E[构建完整src_path]
    E --> F[计算文件MD5]
    F --> G{MD5计算成功?}
    G -->|否| H[记录MD5计算错误]
    G -->|是| I[查询本地数据库]
    I --> J{本地数据库已存在?}
    J -->|是| K[跳过处理]
    J -->|否| L[查询大数据库]
    L --> M{大数据库已存在?}
    M -->|是| N[写入本地数据库]
    M -->|否| O[调用VirusTotal API]
    O --> P{API调用成功?}
    P -->|否| Q[记录API错误]
    Q --> R[写入本地数据库]
    R --> S[标记为未扫描]
    P -->|是| T[解析VT扫描结果]
    T --> U[计算检测率]
    U --> V[写入本地数据库]
    V --> W[更新统计信息]
    N --> W
    S --> W
    K --> AA{还有文件?}
    W --> AA
    H --> AA
    AA -->|是| B
    AA -->|否| BB[处理完成]
```

## 情报网数据库批量写入流程

```mermaid
flowchart TD
    A[文件处理完成] --> B{是精确目录?}
    B -->|否| C[跳过情报网写入]
    B -->|是| D[连接情报网数据库]
    D --> E{连接成功?}
    E -->|否| F[记录连接错误]
    E -->|是| G[查询今日精确目录样本]
    G --> H{有样本需要同步?}
    H -->|否| I[记录无新样本]
    H -->|是| J[遍历所有样本]
    J --> K[检查样本是否已存在]
    K --> L{已存在?}
    L -->|是| M[跳过样本]
    L -->|否| N[获取VT首次提交时间]
    N --> O[设置默认值]
    O --> P[提取病毒家族]
    P --> Q[获取下一个序号]
    Q --> R[写入情报网数据库]
    R --> S{写入成功?}
    S -->|否| T[记录写入错误]
    S -->|是| U[更新同步统计]
    M --> V{还有样本?}
    T --> V
    U --> V
    V -->|是| J
    V -->|否| W[完成批量同步]
    F --> W
    I --> W
    C --> W
```

## 关键特点

### 1. 处理顺序
- **文件处理**: 先完成所有文件的MD5计算和主数据库写入
- **报告生成**: 生成Excel统计报告
- **样本分类**: 分类到恶意/清洁文件夹
- **数据库同步**: 写入恶意软件和勒索软件数据库
- **情报网写入**: 最后批量写入情报网数据库

### 2. 批量处理优势
- **效率更高**: 一次性处理所有样本
- **连接优化**: 减少数据库连接次数
- **错误处理**: 简化错误处理逻辑
- **性能更好**: 批量操作比逐个处理更快

### 3. 数据完整性
- **保留VT首次提交时间**: 从主数据库获取VT首次提交时间
- **自动去重**: 避免重复写入
- **完整记录**: 保留所有必要信息

## 配置说明

### 数据库配置
```python
# 主数据库配置
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'Mp2010.18',
    'database': 'qingbao',
    'port': 3306,
    'charset': 'utf8mb4'
}

# 情报网数据库配置（相同服务器）
QINGBAO_DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'Mp2010.18',
    'database': 'qingbao',
    'port': 3306,
    'charset': 'utf8mb4'
}
```

### 监控目录配置
```python
PRECISE_WATCH_DIR = r'G:\s1.原始综合样本-malware'  # 精确监控目录
IMPRECISE_WATCH_DIR = r'G:\s2.其他样本'  # 非精确监控目录
```

## 日志输出示例

```
🆕 检测到新压缩包: sample.zip (来自: G:\s1.原始综合样本-malware)
📁 解压目标目录: G:/2024-12-19
📊 开始处理解压文件...
💾 写入数据库: malware.exe
✅ 数据库写入成功(VT查询): malware.exe | MD5: abc123 | MREDR: detected | VT首次提交: 2024-01-15
💾 写入数据库: clean.exe
✅ 数据库写入成功(VT查询): clean.exe | MD5: def456 | MREDR: clean | VT首次提交: 2024-01-20
📊 开始生成报告和分类样本...
✅ Excel报告已生成: G:/2024-12-19/检测报告.xlsx
✅ 样本分类完成
✅ 恶意软件数据库同步完成
✅ 勒索软件数据库同步完成
🔗 开始写入情报网数据库...
📊 发现 2 个精确目录样本需要写入情报网
✅ 样本 abc123 已写入情报网数据库 (序号: 1001, 类型: PE文件, VT首次提交: 2024-01-15)
✅ 样本 def456 已写入情报网数据库 (序号: 1002, 类型: 动态链接库, VT首次提交: 2024-01-20)
✅ 成功写入情报网数据库: 2/2 个样本
```

## 优势特点

### 1. 处理效率高
- 批量处理所有样本
- 减少数据库连接开销
- 提高整体处理速度

### 2. 逻辑清晰
- 处理步骤明确
- 错误处理简单
- 便于维护和调试

### 3. 数据一致性好
- 所有文件处理完成后统一写入
- 避免部分数据丢失
- 保证数据完整性

### 4. 资源利用优化
- 减少数据库连接次数
- 降低系统资源消耗
- 提高程序稳定性

## 注意事项

1. **处理顺序**: 情报网写入在所有文件处理完成后进行
2. **VT首次提交时间**: 从主数据库获取VT首次提交时间，保留重要时间信息
3. **错误处理**: 情报网写入失败不影响主流程
4. **性能考虑**: 批量处理提高效率，但可能增加延迟
5. **监控建议**: 建议监控批量写入的成功率和耗时 