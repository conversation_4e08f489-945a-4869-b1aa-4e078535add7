#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试病毒家族提取逻辑
"""

import sys
import os

# 添加virus_clean模块路径
sys.path.append('virus_clean')

def test_virus_family_extraction():
    """测试病毒家族提取功能"""
    print("🧪 测试病毒家族提取功能")
    print("=" * 60)
    
    try:
        from auto_unzip_and_md5 import extract_virus_family
        
        # 测试用例：模拟不同引擎的检测结果
        test_cases = [
            {
                "name": "多个引擎检测到Trojan",
                "engine_results": {
                    "Kaspersky": "Trojan.Win32.Agent.abc",
                    "ESET-NOD32": "Win32/Trojan.Agent.xyz",
                    "BitDefender": "Trojan.Generic.123",
                    "Symantec": "clean",
                    "Microsoft": "not_scanned"
                },
                "expected": "Trojan"
            },
            {
                "name": "检测到勒索软件",
                "engine_results": {
                    "Kaspersky": "Trojan-Ransom.Win32.Locky",
                    "ESET-NOD32": "Win32/Filecoder.Locky",
                    "BitDefender": "Ransom.Locky.Gen",
                    "Symantec": "Ransom.Locky",
                    "Microsoft": "Ransom:Win32/Locky"
                },
                "expected": "Ransomware"
            },
            {
                "name": "检测到广告软件",
                "engine_results": {
                    "Kaspersky": "not-a-virus:AdWare.Win32.Agent",
                    "ESET-NOD32": "Win32/Adware.Agent",
                    "BitDefender": "Adware.Generic",
                    "Symantec": "clean",
                    "Microsoft": "Adware:Win32/Agent"
                },
                "expected": "Adware"
            },
            {
                "name": "检测到间谍软件",
                "engine_results": {
                    "Kaspersky": "Trojan-Spy.Win32.Agent",
                    "ESET-NOD32": "Win32/Spy.Agent",
                    "BitDefender": "Spyware.Generic",
                    "Symantec": "Spyware.Agent",
                    "Microsoft": "TrojanSpy:Win32/Agent"
                },
                "expected": "Spyware"
            },
            {
                "name": "检测到后门",
                "engine_results": {
                    "Kaspersky": "Backdoor.Win32.Agent",
                    "ESET-NOD32": "Win32/Backdoor.Agent",
                    "BitDefender": "Backdoor.Generic",
                    "Symantec": "Backdoor.Agent",
                    "Microsoft": "Backdoor:Win32/Agent"
                },
                "expected": "Backdoor"
            },
            {
                "name": "具体Trojan家族",
                "engine_results": {
                    "Kaspersky": "Trojan.Win32.Emotet.abc",
                    "ESET-NOD32": "Win32/Trojan.Emotet",
                    "BitDefender": "Trojan.Emotet.Gen",
                    "Symantec": "Trojan.Emotet",
                    "Microsoft": "Trojan:Win32/Emotet"
                },
                "expected": "Trojan.Emotet"
            },
            {
                "name": "全部clean",
                "engine_results": {
                    "Kaspersky": "clean",
                    "ESET-NOD32": "clean",
                    "BitDefender": "clean",
                    "Symantec": "clean",
                    "Microsoft": "clean"
                },
                "expected": ""
            },
            {
                "name": "混合结果",
                "engine_results": {
                    "Kaspersky": "Trojan.Win32.Agent",
                    "ESET-NOD32": "clean",
                    "BitDefender": "not_scanned",
                    "Symantec": "timeout",
                    "Microsoft": "error"
                },
                "expected": "Trojan"
            }
        ]
        
        print("测试用例:")
        print("-" * 30)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"{i}. {test_case['name']}")
            
            # 显示引擎结果
            print("   引擎检测结果:")
            for engine, result in test_case['engine_results'].items():
                print(f"     {engine}: {result}")
            
            # 提取病毒家族
            extracted_family = extract_virus_family(test_case['engine_results'])
            expected_family = test_case['expected']
            
            print(f"   提取结果: '{extracted_family}'")
            print(f"   期望结果: '{expected_family}'")
            
            if extracted_family == expected_family:
                print("   ✅ 测试通过")
            else:
                print("   ❌ 测试失败")
            
            print()
        
        print("=" * 60)
        print("🎯 病毒家族提取逻辑说明:")
        print("1. 优先提取通用家族类型（Trojan、Ransomware、Adware等）")
        print("2. 如果检测到Trojan，尝试提取具体家族名（如Emotet、Agent等）")
        print("3. 返回最常见的家族名称")
        print("4. 如果没有检测到恶意行为，返回空字符串")
        
        print()
        print("💡 VT集成说明:")
        print("- 如果VT返回多个家族，取第一个（最常见的）")
        print("- 文件类型直接从VT的type_description或magic字段获取")
        print("- 收集渠道从主表的source字段获取")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_virus_family_extraction()
