#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查脚本 - 检查目标电脑是否满足程序运行条件
"""

import os
import sys
import subprocess
import socket
import winreg
import shutil

def check_python():
    """检查Python环境"""
    print("🐍 检查Python环境...")
    print(f"   Python版本: {sys.version}")
    print(f"   Python路径: {sys.executable}")
    return True

def check_winrar():
    """检查WinRAR是否安装"""
    print("🗜️  检查WinRAR...")
    
    # 常见路径
    possible_paths = [
        r"C:\Program Files\WinRAR\WinRAR.exe",
        r"C:\Program Files (x86)\WinRAR\WinRAR.exe"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"   ✅ 找到WinRAR: {path}")
            return True
    
    # 检查注册表
    try:
        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\WinRAR.exe")
        value, _ = winreg.QueryValueEx(key, "")
        if os.path.exists(value):
            print(f"   ✅ 找到WinRAR (注册表): {value}")
            return True
    except Exception:
        pass
    
    # 检查PATH
    winrar_path = shutil.which("WinRAR.exe")
    if winrar_path:
        print(f"   ✅ 找到WinRAR (PATH): {winrar_path}")
        return True
    
    print("   ❌ 未找到WinRAR，请安装WinRAR")
    print("   💡 下载地址: https://www.win-rar.com/download.html")
    return False

def check_database_connection():
    """检查数据库连接"""
    print("🗄️  检查数据库连接...")
    
    db_host = "*************"
    db_port = 3306
    
    try:
        # 尝试连接数据库服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((db_host, db_port))
        sock.close()
        
        if result == 0:
            print(f"   ✅ 可以连接到数据库服务器 {db_host}:{db_port}")
            return True
        else:
            print(f"   ❌ 无法连接到数据库服务器 {db_host}:{db_port}")
            return False
    except Exception as e:
        print(f"   ❌ 数据库连接测试失败: {e}")
        return False

def check_internet_connection():
    """检查网络连接"""
    print("🌐 检查网络连接...")
    
    try:
        # 测试连接到VirusTotal
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(("www.virustotal.com", 443))
        sock.close()
        
        if result == 0:
            print("   ✅ 可以连接到VirusTotal")
            return True
        else:
            print("   ❌ 无法连接到VirusTotal")
            return False
    except Exception as e:
        print(f"   ❌ 网络连接测试失败: {e}")
        return False

def check_disk_space():
    """检查磁盘空间"""
    print("💾 检查磁盘空间...")
    
    try:
        # 检查当前目录所在磁盘的空间
        statvfs = os.statvfs('.')
        free_space = statvfs.f_frsize * statvfs.f_bavail
        free_gb = free_space / (1024**3)
        
        print(f"   可用空间: {free_gb:.2f} GB")
        
        if free_gb > 1.0:  # 至少1GB空间
            print("   ✅ 磁盘空间充足")
            return True
        else:
            print("   ⚠️  磁盘空间不足，建议至少保留1GB空间")
            return False
    except:
        # Windows系统使用不同的方法
        try:
            import shutil
            total, used, free = shutil.disk_usage('.')
            free_gb = free / (1024**3)
            
            print(f"   可用空间: {free_gb:.2f} GB")
            
            if free_gb > 1.0:
                print("   ✅ 磁盘空间充足")
                return True
            else:
                print("   ⚠️  磁盘空间不足，建议至少保留1GB空间")
                return False
        except Exception as e:
            print(f"   ❌ 无法检查磁盘空间: {e}")
            return False

def check_permissions():
    """检查文件权限"""
    print("🔐 检查文件权限...")
    
    try:
        # 测试当前目录的写权限
        test_file = "test_write_permission.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        
        print("   ✅ 具有文件写入权限")
        return True
    except Exception as e:
        print(f"   ❌ 文件权限不足: {e}")
        return False

def main():
    print("🔍 环境检查工具")
    print("=" * 50)
    
    checks = [
        ("Python环境", check_python),
        ("WinRAR工具", check_winrar),
        ("数据库连接", check_database_connection),
        ("网络连接", check_internet_connection),
        ("磁盘空间", check_disk_space),
        ("文件权限", check_permissions)
    ]
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"   ❌ 检查{name}时发生错误: {e}")
            results.append((name, False))
        print()
    
    # 总结
    print("=" * 50)
    print("📊 检查结果总结:")
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有检查都通过！程序应该可以正常运行。")
    else:
        print("⚠️  部分检查未通过，程序可能无法正常运行。")
        print("💡 请根据上面的提示解决相关问题。")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    main() 