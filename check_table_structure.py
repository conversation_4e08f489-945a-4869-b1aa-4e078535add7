#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查数据库表结构
"""

import sys
import os

# 添加virus_clean模块路径
sys.path.append('virus_clean')

def check_table_structure():
    """检查数据库表结构"""
    print("🔍 检查数据库表结构")
    print("=" * 60)
    
    try:
        from auto_unzip_and_md5 import DB_CONFIG, TABLE_NAME
        import pymysql
        
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 1. 检查主表结构
        print(f"1. 检查主表 {TABLE_NAME} 结构:")
        print("-" * 30)
        
        cursor.execute(f"DESCRIBE `{TABLE_NAME}`")
        columns = cursor.fetchall()
        
        print("   字段列表:")
        for column in columns:
            field, type_info, null, key, default, extra = column
            print(f"   {field:<25} | {type_info:<20} | NULL: {null:<3} | Key: {key:<3}")
        
        # 检查是否有vt_file_type字段
        vt_file_type_exists = any(col[0] == 'vt_file_type' for col in columns)
        virus_family_exists = any(col[0] == 'virus_family' for col in columns)
        
        print()
        print("   VT相关字段检查:")
        print(f"   vt_file_type: {'✅ 存在' if vt_file_type_exists else '❌ 不存在'}")
        print(f"   virus_family: {'✅ 存在' if virus_family_exists else '❌ 不存在'}")
        
        cursor.close()
        conn.close()
        
        print()
        print("=" * 60)
        
        if not vt_file_type_exists or not virus_family_exists:
            print("⚠️  需要添加缺失的字段!")
            print()
            print("建议执行以下SQL语句:")
            
            if not vt_file_type_exists:
                print(f"ALTER TABLE `{TABLE_NAME}` ADD COLUMN `vt_file_type` VARCHAR(100) DEFAULT '' COMMENT 'VT文件类型';")
            
            if not virus_family_exists:
                print(f"ALTER TABLE `{TABLE_NAME}` ADD COLUMN `virus_family` VARCHAR(100) DEFAULT '' COMMENT '病毒家族';")
        else:
            print("✅ 所有必需字段都存在")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_structure()
