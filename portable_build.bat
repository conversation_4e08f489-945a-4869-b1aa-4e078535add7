@echo off
echo 创建便携式打包...
echo.

echo 1. 安装依赖...
pip install -r requirements.txt
pip install pyinstaller

echo.
echo 2. 创建便携式打包...
pyinstaller --onefile --console --hidden-import=requests --hidden-import=pandas --hidden-import=openpyxl --hidden-import=openpyxl.styles --hidden-import=pymysql --hidden-import=pyzipper --add-data "requirements.txt;." virus_clean\auto_unzip_and_md5.py

echo.
echo 3. 创建发布文件夹...
if not exist "portable_release" mkdir portable_release
copy dist\auto_unzip_and_md5.exe portable_release\
copy requirements.txt portable_release\
copy "使用说明.md" portable_release\
copy "功能总结.md" portable_release\

echo.
echo 4. 创建运行说明...
echo 便携式程序使用说明: > portable_release\运行说明.txt
echo. >> portable_release\运行说明.txt
echo 1. 确保目标电脑已安装WinRAR >> portable_release\运行说明.txt
echo 2. 确保网络连接正常（用于VirusTotal查询） >> portable_release\运行说明.txt
echo 3. 确保可以连接到数据库服务器 >> portable_release\运行说明.txt
echo 4. 直接双击 auto_unzip_and_md5.exe 运行 >> portable_release\运行说明.txt
echo. >> portable_release\运行说明.txt
echo 程序会自动提示输入监控目录和解压目录 >> portable_release\运行说明.txt

echo.
echo ✅ 便携式打包完成！
echo 📁 发布文件夹: portable_release\
echo 📄 主程序: portable_release\auto_unzip_and_md5.exe
echo.
pause 