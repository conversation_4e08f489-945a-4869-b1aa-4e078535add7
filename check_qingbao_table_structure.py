#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查情报网表结构
"""

import sys
import os

# 添加virus_clean模块路径
sys.path.append('virus_clean')

def check_qingbao_table_structure():
    """检查情报网表结构"""
    print("🔍 检查情报网表结构")
    print("=" * 60)
    
    try:
        from auto_unzip_and_md5 import DB_CONFIG, QINGBAO_TABLE
        import pymysql
        
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 查看表结构
        print(f"1. 查看 {QINGBAO_TABLE} 表结构:")
        print("-" * 30)
        
        cursor.execute(f"DESCRIBE `{QINGBAO_TABLE}`")
        columns = cursor.fetchall()
        
        for column in columns:
            field, type_info, null, key, default, extra = column
            print(f"   {field:<20} | {type_info:<15} | NULL: {null:<3} | Key: {key:<3} | Default: {default}")
        
        print()
        
        # 特别检查first_submit_time字段
        print("2. 检查first_submit_time字段:")
        print("-" * 30)
        
        first_submit_time_found = False
        for column in columns:
            field, type_info, null, key, default, extra = column
            if 'first_submit' in field.lower() or 'submit_time' in field.lower():
                print(f"   找到字段: {field}")
                print(f"   类型: {type_info}")
                print(f"   允许NULL: {null}")
                print(f"   默认值: {default}")
                first_submit_time_found = True
        
        if not first_submit_time_found:
            print("   ❌ 未找到first_submit_time相关字段")
        
        print()
        
        # 检查最近的记录
        print("3. 检查最近的记录:")
        print("-" * 30)
        
        cursor.execute(f"""
            SELECT id, file_name, first_submit_time, created_at
            FROM `{QINGBAO_TABLE}`
            ORDER BY id DESC
            LIMIT 5
        """)
        
        recent_records = cursor.fetchall()
        if recent_records:
            print("   最近5条记录:")
            for record in recent_records:
                record_id, file_name, first_submit_time, created_at = record
                print(f"   ID: {record_id} | 文件名: {file_name[:20]}")
                print(f"      首次提交时间: {first_submit_time}")
                print(f"      创建时间: {created_at}")
                print()
        else:
            print("   ❌ 表中没有记录")
        
        cursor.close()
        conn.close()
        
        print("=" * 60)
        print("💡 说明:")
        print("- 如果first_submit_time是DATETIME类型，可以直接存储datetime对象")
        print("- 如果是VARCHAR类型，需要存储字符串格式")
        print("- 当前代码已将VT时间格式化为 'YYYY-MM-DD HH:MM:SS' 格式")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_qingbao_table_structure()
