#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

def test_function_returns():
    """测试各个函数是否正确返回数量"""
    
    # 导入修改后的函数
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import (
        write_malware_to_hash_all, 
        write_ransom_to_ransom_db, 
        classify_samples_by_detection,
        write_precise_samples_to_qingbao
    )
    
    print("测试函数返回值修复:")
    print("=" * 50)
    
    # 测试write_malware_to_hash_all
    print("1. 测试 write_malware_to_hash_all()...")
    try:
        result = write_malware_to_hash_all()
        print(f"   返回值: {result} (类型: {type(result)})")
        if isinstance(result, int):
            print("   ✅ 正确返回整数")
        else:
            print("   ❌ 返回值类型错误")
    except Exception as e:
        print(f"   ❌ 函数调用失败: {e}")
    
    print()
    
    # 测试write_ransom_to_ransom_db
    print("2. 测试 write_ransom_to_ransom_db()...")
    try:
        result = write_ransom_to_ransom_db()
        print(f"   返回值: {result} (类型: {type(result)})")
        if isinstance(result, int):
            print("   ✅ 正确返回整数")
        else:
            print("   ❌ 返回值类型错误")
    except Exception as e:
        print(f"   ❌ 函数调用失败: {e}")
    
    print()
    
    # 测试classify_samples_by_detection
    print("3. 测试 classify_samples_by_detection()...")
    try:
        import tempfile
        temp_dir = tempfile.mkdtemp()
        result = classify_samples_by_detection(temp_dir)
        print(f"   返回值: {result} (类型: {type(result)})")
        if isinstance(result, tuple) and len(result) == 2:
            clean_count, not_scan_count = result
            if isinstance(clean_count, int) and isinstance(not_scan_count, int):
                print("   ✅ 正确返回元组(clean_count, not_scan_count)")
            else:
                print("   ❌ 元组内容类型错误")
        else:
            print("   ❌ 返回值类型错误，应该是元组")
        
        # 清理临时目录
        import shutil
        shutil.rmtree(temp_dir)
    except Exception as e:
        print(f"   ❌ 函数调用失败: {e}")
    
    print()
    
    # 测试write_precise_samples_to_qingbao
    print("4. 测试 write_precise_samples_to_qingbao()...")
    try:
        result = write_precise_samples_to_qingbao()
        print(f"   返回值: {result} (类型: {type(result)})")
        if isinstance(result, int):
            print("   ✅ 正确返回整数")
        else:
            print("   ❌ 返回值类型错误")
    except Exception as e:
        print(f"   ❌ 函数调用失败: {e}")
    
    print()
    print("=" * 50)
    print("总结:")
    print("- 所有函数现在都应该返回实际的处理数量")
    print("- write_malware_to_hash_all(): 返回写入大库的数量")
    print("- write_ransom_to_ransom_db(): 返回写入小库的数量") 
    print("- classify_samples_by_detection(): 返回(clean数量, not_scan数量)")
    print("- write_precise_samples_to_qingbao(): 返回写入情报网的数量")

if __name__ == "__main__":
    test_function_returns()
