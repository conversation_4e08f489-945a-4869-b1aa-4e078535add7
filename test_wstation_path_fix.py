#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import tempfile

def test_wstation_path_fix():
    """测试修复后的W站路径判断逻辑"""
    
    # 导入修改后的函数
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import is_executable_file
    
    # 测试路径（使用相对路径避免权限问题）
    test_paths = [
        # 你的实际路径格式
        "z1.精洗/W站/sample1.txt",
        "z1.精洗/W站/subfolder/sample2.bin",
        "extract/W站/malware.dat",

        # 其他W站路径格式
        "W站/file.txt",
        "some/path/W站/file.bin",
        "extract/20250725/W站/sample.exe",

        # 非W站路径（应该返回False，因为没有PE头）
        "z1.精洗/normal/file.txt",
        "other/folder/file.bin",
        "W站相关/file.txt",  # 包含W站字符但不是W站文件夹
    ]
    
    print("测试修复后的W站路径判断逻辑:")
    print("=" * 60)
    
    # 创建临时目录和文件进行测试
    with tempfile.TemporaryDirectory() as temp_dir:
        for test_path in test_paths:
            # 创建完整路径
            full_path = os.path.join(temp_dir, test_path.replace('/', os.sep))
            
            # 创建目录
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            # 创建测试文件（写入非可执行内容）
            with open(full_path, 'wb') as f:
                f.write(b'This is test data, not executable')
            
            # 测试判断结果
            result = is_executable_file(full_path)
            
            # 分析路径
            normalized = os.path.normpath(full_path).replace('\\', '/')
            path_parts = normalized.split('/')
            has_wstation = 'W站' in path_parts
            
            status = "✅ 正确" if result == has_wstation else "❌ 错误"
            wstation_mark = "🎯 W站" if has_wstation else "📁 普通"
            
            print(f"{status} | {wstation_mark} | {result} | {test_path}")
    
    print()
    print("修复说明:")
    print("- 原来的逻辑: 只检查 '/W站/' 和 'W站/' 开头")
    print("- 修复后逻辑: 检查路径分割后是否包含 'W站' 文件夹")
    print("- 现在可以正确识别 'G:/z1.精洗/W站/文件' 这种路径")

if __name__ == "__main__":
    test_wstation_path_fix()
