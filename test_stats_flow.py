#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import tempfile
import shutil
import datetime

def test_stats_flow():
    """测试完整的统计流程，找出问题所在"""
    
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import stats, write_to_db, flatten_and_extract_all, is_executable_file
    
    print("🔍 测试统计流程:")
    print("=" * 60)
    
    # 1. 检查初始状态
    print(f"1. 初始stats状态: {stats}")
    initial_files_in_db = stats.get('files_in_db', 0)
    print(f"   初始files_in_db: {initial_files_in_db}")
    print()
    
    # 2. 创建测试文件
    print("2. 创建测试文件:")
    temp_dir = tempfile.mkdtemp()
    print(f"   临时目录: {temp_dir}")
    
    # 创建一个测试可执行文件
    test_file = os.path.join(temp_dir, "test_sample.exe")
    with open(test_file, "wb") as f:
        f.write(b"MZ" + b"A" * 1000)  # 模拟PE文件
    
    print(f"   创建测试文件: {test_file}")
    print(f"   文件大小: {os.path.getsize(test_file)} bytes")
    
    # 检查是否被识别为可执行文件
    is_exec = is_executable_file(test_file)
    print(f"   是否识别为可执行文件: {is_exec}")
    print()
    
    # 3. 测试write_to_db函数
    print("3. 测试write_to_db函数:")
    
    # 记录调用前的状态
    before_call = stats.get('files_in_db', 0)
    print(f"   调用前files_in_db: {before_call}")
    
    # 调用write_to_db
    try:
        import hashlib
        with open(test_file, "rb") as f:
            test_md5 = hashlib.md5(f.read()).hexdigest()
        
        print(f"   测试文件MD5: {test_md5}")
        
        result = write_to_db(
            md5=test_md5,
            filename="test_sample.exe",
            file_size=os.path.getsize(test_file),
            date=datetime.datetime.now().strftime('%Y-%m-%d'),
            src_path=f"{temp_dir} > test_sample.exe",
            source_dir_path=temp_dir,
            source="测试"
        )
        
        print(f"   write_to_db返回值: {result}")
        
        # 检查调用后的状态
        after_call = stats.get('files_in_db', 0)
        print(f"   调用后files_in_db: {after_call}")
        print(f"   差值: {after_call - before_call}")
        
    except Exception as e:
        print(f"   ❌ write_to_db调用失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # 4. 测试flatten_and_extract_all函数
    print("4. 测试flatten_and_extract_all函数:")
    
    before_flatten = stats.get('files_in_db', 0)
    print(f"   调用前files_in_db: {before_flatten}")
    
    try:
        # 模拟W站路径
        fake_zip_path = "G:\\z1.精洗\\W站\\test.rar"
        fake_watch_dir = "G:\\z1.精洗\\W站"
        
        flatten_and_extract_all(temp_dir, fake_zip_path, fake_watch_dir, 'W站')
        
        after_flatten = stats.get('files_in_db', 0)
        print(f"   调用后files_in_db: {after_flatten}")
        print(f"   差值: {after_flatten - before_flatten}")
        
    except Exception as e:
        print(f"   ❌ flatten_and_extract_all调用失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # 5. 模拟完整的统计逻辑
    print("5. 模拟完整的统计逻辑:")
    
    # 模拟主程序中的统计逻辑
    before_files_in_db = stats.get('files_in_db', 0)
    print(f"   记录before_files_in_db: {before_files_in_db}")
    
    # 再次调用flatten_and_extract_all（模拟主程序流程）
    try:
        flatten_and_extract_all(temp_dir, fake_zip_path, fake_watch_dir, 'W站')
        
        after_files_in_db = stats.get('files_in_db', 0)
        print(f"   处理后files_in_db: {after_files_in_db}")
        
        main_table_written = after_files_in_db - before_files_in_db
        print(f"   计算的写入数量: {main_table_written}")
        
    except Exception as e:
        print(f"   ❌ 模拟统计失败: {e}")
    
    print()
    
    # 6. 检查stats变量的内存地址
    print("6. 检查stats变量:")
    print(f"   stats对象ID: {id(stats)}")
    print(f"   stats内容: {stats}")
    
    # 7. 清理
    print()
    print("7. 清理临时文件...")
    try:
        shutil.rmtree(temp_dir)
        print("   ✅ 临时文件清理完成")
    except Exception as e:
        print(f"   ⚠️  清理失败: {e}")
    
    print()
    print("=" * 60)
    print("🎯 分析结论:")
    print("1. 检查write_to_db是否正确返回True")
    print("2. 检查stats['files_in_db']是否正确增加")
    print("3. 检查统计时机是否正确")
    print("4. 检查是否有其他地方重置了stats")

if __name__ == "__main__":
    test_stats_flow()
