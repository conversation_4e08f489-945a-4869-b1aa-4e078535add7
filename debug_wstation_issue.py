#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

def debug_wstation_issue():
    """调试W站文件夹判断问题"""
    
    # 导入函数
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import is_executable_file
    
    print("W站文件夹判断问题调试:")
    print("=" * 60)
    
    # 请提供你实际的文件路径
    print("请提供以下信息:")
    print("1. 那6个被判定为非可执行文件的完整路径")
    print("2. 解压目录的完整路径")
    print()
    
    # 测试函数
    def test_path(file_path):
        print(f"测试路径: {file_path}")
        print("-" * 40)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print("❌ 文件不存在")
            return
        
        # 路径标准化
        normalized_path = os.path.normpath(file_path).replace('\\', '/')
        print(f"标准化路径: {normalized_path}")
        
        # 路径分割
        path_parts = normalized_path.split('/')
        print(f"路径分割: {path_parts}")
        
        # 检查是否包含W站
        has_wstation = 'W站' in path_parts
        print(f"包含W站文件夹: {has_wstation}")
        
        # 检查文件头
        try:
            with open(file_path, 'rb') as f:
                head = f.read(16)
                print(f"文件头 (16字节): {head.hex()}")
                print(f"文件头 (ASCII): {head}")
        except Exception as e:
            print(f"读取文件头失败: {e}")
        
        # 扩展名
        ext = os.path.splitext(file_path)[1].lower()
        print(f"文件扩展名: '{ext}'")
        
        # 调用判断函数
        result = is_executable_file(file_path)
        print(f"is_executable_file 结果: {result}")
        
        print()
    
    # 示例测试（请替换为你的实际路径）
    example_paths = [
        # 请替换为你实际的文件路径
        # "G:\\z1.精洗\\W站\\sample1.txt",
        # "G:\\z1.精洗\\W站\\sample2.bin",
    ]
    
    if example_paths:
        for path in example_paths:
            test_path(path)
    else:
        print("请修改脚本，添加你实际的文件路径进行测试")
        print()
        print("示例用法:")
        print("test_path('G:\\\\z1.精洗\\\\W站\\\\your_file_name')")
    
    print("=" * 60)
    print("可能的问题:")
    print("1. 文件路径中的W站文件夹名称不是标准的'W站'")
    print("2. 路径编码问题")
    print("3. 使用了旧版本的exe文件")
    print("4. 文件实际不在W站文件夹中")
    print("5. 程序中有其他地方调用了旧的判断逻辑")

if __name__ == "__main__":
    debug_wstation_issue()
