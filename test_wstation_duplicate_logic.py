#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import tempfile

def test_wstation_duplicate_logic():
    """测试W站路径下非可执行文件且MD5重复的处理逻辑"""
    
    # 导入函数
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import is_executable_file, calc_md5
    
    print("测试W站路径下重复文件的处理逻辑:")
    print("=" * 60)
    
    # 创建测试环境
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建W站文件夹
        w_station_dir = os.path.join(temp_dir, "W站")
        os.makedirs(w_station_dir, exist_ok=True)
        
        # 测试场景
        test_scenarios = [
            {
                "name": "W站路径下的文本文件",
                "path": os.path.join(w_station_dir, "test.txt"),
                "content": b"This is a text file in W station",
                "description": "W站路径 + 非PE头 + 非脚本扩展名"
            },
            {
                "name": "W站路径下的数据文件",
                "path": os.path.join(w_station_dir, "data.bin"),
                "content": b"\x00\x01\x02\x03\x04\x05",
                "description": "W站路径 + 二进制数据 + 非脚本扩展名"
            },
            {
                "name": "W站路径下的无扩展名文件",
                "path": os.path.join(w_station_dir, "sample_file"),
                "content": b"Random data without extension",
                "description": "W站路径 + 无扩展名 + 非PE头"
            },
            {
                "name": "W站路径下的PE文件",
                "path": os.path.join(w_station_dir, "malware.exe"),
                "content": b'MZ\x90\x00\x03\x00\x00\x00\x04\x00\x00\x00\xff\xff\x00\x00',
                "description": "W站路径 + PE头 + exe扩展名"
            }
        ]
        
        print("测试各种场景下的可执行文件判断:")
        print("-" * 50)
        
        for scenario in test_scenarios:
            # 创建文件
            with open(scenario["path"], 'wb') as f:
                f.write(scenario["content"])
            
            # 测试可执行文件判断
            is_exec = is_executable_file(scenario["path"])
            md5 = calc_md5(scenario["path"])
            
            print(f"场景: {scenario['name']}")
            print(f"  描述: {scenario['description']}")
            print(f"  路径: {scenario['path']}")
            print(f"  可执行判断: {is_exec}")
            print(f"  MD5: {md5}")
            print()
        
        print("=" * 60)
        print("处理逻辑分析:")
        print()
        
        print("根据代码流程 (flatten_and_extract_all函数第446行):")
        print("```python")
        print("if not is_executable_file(file_path):")
        print("    logging.info(f'[SKIP] 非可执行文件不写入数据库: {file_path}')")
        print("    continue")
        print("```")
        print()
        
        print("对于W站路径下的文件:")
        print("1. W站特殊规则会让 is_executable_file() 直接返回 True")
        print("2. 无论文件实际内容是什么，都会被判定为可执行文件")
        print("3. 因此会继续执行后续的MD5计算和数据库写入流程")
        print("4. 如果MD5重复，会记录 '⚠️ MD5已存在，跳过写入' 日志")
        print("5. 如果MD5不重复，会正常写入数据库")
        print()
        
        print("结论:")
        print("W站路径下的文件 + MD5重复 = '⚠️ MD5已存在，跳过写入' 日志")
        print("W站路径下的文件 + MD5不重复 = 正常写入数据库")
        print("W站路径下的文件不会出现 '[SKIP] 非可执行文件' 日志")

if __name__ == "__main__":
    test_wstation_duplicate_logic()
