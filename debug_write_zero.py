#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import datetime

def debug_write_zero():
    """调试为什么写入数量还是0"""
    
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import DB_CONFIG, TABLE_NAME, QINGBAO_TABLE
    import pymysql
    
    print("调试写入数量为0的问题:")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        
        # 1. 检查今日主表数据的详细信息
        print(f"1. 检查今日主表 {TABLE_NAME} 详细数据:")
        
        cursor.execute(f"""
            SELECT md5, file_name, file_size, src_path, get_date, source, `repeat`
            FROM `{TABLE_NAME}` 
            WHERE date = %s 
            ORDER BY id DESC 
            LIMIT 10
        """, (today,))
        main_records = cursor.fetchall()
        
        print(f"   今日主表记录数: {len(main_records)}")
        
        if main_records:
            repeat_count = 0
            new_count = 0
            
            print("   最近10条记录:")
            for i, record in enumerate(main_records, 1):
                md5, file_name, file_size, src_path, get_date, source, repeat = record
                repeat_status = "重复" if repeat == 1 else "新增"
                if repeat == 1:
                    repeat_count += 1
                else:
                    new_count += 1
                    
                print(f"   {i:2d}. {repeat_status} | {file_name[:30]:<30} | MD5: {md5[:8]}... | Source: {source}")
            
            print(f"\n   统计: 新增={new_count}, 重复={repeat_count}")
        
        print()
        
        # 2. 检查最近的处理时间
        print("2. 检查最近的处理时间:")
        
        cursor.execute(f"""
            SELECT MAX(get_date), MIN(get_date), COUNT(*)
            FROM `{TABLE_NAME}` 
            WHERE date = %s
        """, (today,))
        time_info = cursor.fetchone()
        
        if time_info and time_info[0]:
            max_time, min_time, count = time_info
            print(f"   最新记录时间: {max_time}")
            print(f"   最早记录时间: {min_time}")
            print(f"   总记录数: {count}")
        
        print()
        
        # 3. 检查是否有新增记录（repeat=0）
        print("3. 检查新增记录（repeat=0）:")
        
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM `{TABLE_NAME}` 
            WHERE date = %s AND `repeat` = 0
        """, (today,))
        new_records_count = cursor.fetchone()[0]
        
        print(f"   今日新增记录数（repeat=0）: {new_records_count}")
        
        if new_records_count > 0:
            cursor.execute(f"""
                SELECT md5, file_name, get_date
                FROM `{TABLE_NAME}` 
                WHERE date = %s AND `repeat` = 0
                ORDER BY id DESC 
                LIMIT 5
            """, (today,))
            new_records = cursor.fetchall()
            
            print("   最近5条新增记录:")
            for i, record in enumerate(new_records, 1):
                md5, file_name, get_date = record
                print(f"   {i}. {file_name} | MD5: {md5[:8]}... | 时间: {get_date}")
        
        print()
        
        # 4. 检查情报网表的同步情况
        print("4. 检查情报网表同步情况:")
        
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM `{QINGBAO_TABLE}` 
            WHERE DATE(created_at) = %s
        """, (today,))
        qingbao_today_count = cursor.fetchone()[0]
        
        print(f"   今日情报网表记录数: {qingbao_today_count}")
        print(f"   主表记录数: {len(main_records) if main_records else 0}")
        
        if qingbao_today_count != len(main_records):
            print("   ⚠️  主表和情报网表记录数不一致！")
        
        print()
        
        # 5. 模拟统计逻辑
        print("5. 模拟统计逻辑:")
        print("   根据代码逻辑:")
        print("   - main_table_written = stats.get('files_in_db', 0) - before_files_in_db")
        print("   - stats['files_in_db'] 只有在 write_to_db() 返回 True 时才会 +1")
        print("   - write_to_db() 在以下情况返回 True:")
        print("     * MD5不存在且成功写入")
        print("     * MD5在大库中重复但成功写入（repeat=1）")
        print("   - write_to_db() 在以下情况返回 None:")
        print("     * MD5在主表中已存在")
        
        print(f"\n   结论: 如果新增记录数={new_records_count}，但写入数量显示0")
        print("   说明这些记录可能是:")
        print("   1. 在大库中重复的记录（repeat=1）")
        print("   2. 或者统计逻辑有问题")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_write_zero()
