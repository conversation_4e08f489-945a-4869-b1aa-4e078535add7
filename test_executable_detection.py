#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

def is_executable_file_current(file_path):
    """
    当前的判断逻辑（有问题的版本）
    """
    win_exts = ['.exe', '.dll', '.sys', '.com', '.scr', '.bat', '.cmd', '.ps1', '.vbs', '.js']
    ext = os.path.splitext(file_path)[1].lower()
    try:
        with open(file_path, 'rb') as f:
            head = f.read(4)
            if ext in win_exts and head[:2] == b'MZ':
                return True
            if head[:4] == b'\x7fELF':
                return True
    except Exception as e:
        print(f"无法判断文件类型: {file_path}, 错误: {e}")
    return False

def is_executable_file_improved(file_path):
    """
    改进的判断逻辑
    """
    win_exts = ['.exe', '.dll', '.sys', '.com', '.scr', '.bat', '.cmd', '.ps1', '.vbs', '.js']
    ext = os.path.splitext(file_path)[1].lower()
    
    try:
        with open(file_path, 'rb') as f:
            head = f.read(4)
            
            # Windows PE文件：检查MZ头
            if head[:2] == b'MZ':
                return True
                
            # Linux ELF文件
            if head[:4] == b'\x7fELF':
                return True
                
            # 脚本文件：即使没有MZ头，如果有正确扩展名也认为是可执行的
            if ext in ['.bat', '.cmd', '.ps1', '.vbs', '.js', '.py', '.sh']:
                return True
                
            # 其他可能的可执行文件格式
            # DOS可执行文件
            if head[:2] == b'MZ' or head[:2] == b'ZM':
                return True
                
            # Java class文件
            if head[:4] == b'\xca\xfe\xba\xbe':
                return True
                
            # 检查是否是压缩的可执行文件（如UPX压缩）
            if head[:3] == b'UPX':
                return True
                
    except Exception as e:
        print(f"无法判断文件类型: {file_path}, 错误: {e}")
    
    return False

def test_files_in_directory(directory):
    """
    测试目录中的文件
    """
    if not os.path.exists(directory):
        print(f"目录不存在: {directory}")
        return
        
    print(f"测试目录: {directory}")
    print("=" * 80)
    
    files = []
    for root, dirs, filenames in os.walk(directory):
        for filename in filenames:
            file_path = os.path.join(root, filename)
            files.append(file_path)
    
    print(f"找到 {len(files)} 个文件")
    print()
    
    current_count = 0
    improved_count = 0
    
    for file_path in files[:20]:  # 只测试前20个文件
        try:
            current_result = is_executable_file_current(file_path)
            improved_result = is_executable_file_improved(file_path)
            
            if current_result:
                current_count += 1
            if improved_result:
                improved_count += 1
                
            if current_result != improved_result:
                print(f"差异: {os.path.basename(file_path)}")
                print(f"  当前逻辑: {current_result}")
                print(f"  改进逻辑: {improved_result}")
                
                # 显示文件头信息
                try:
                    with open(file_path, 'rb') as f:
                        head = f.read(16)
                        print(f"  文件头: {head.hex()}")
                        print(f"  扩展名: {os.path.splitext(file_path)[1]}")
                except:
                    pass
                print()
                
        except Exception as e:
            print(f"测试文件失败: {file_path}, 错误: {e}")
    
    print(f"当前逻辑识别出的可执行文件: {current_count}")
    print(f"改进逻辑识别出的可执行文件: {improved_count}")

if __name__ == "__main__":
    # 测试当前目录下的解压文件
    test_directory = "."
    if len(sys.argv) > 1:
        test_directory = sys.argv[1]
    
    test_files_in_directory(test_directory)
