('D:\\泯然\\virus_clean\\build\\病毒精洗程序\\病毒精洗程序.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'D:\\泯然\\virus_clean\\build\\病毒精洗程序\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'D:\\泯然\\virus_clean\\build\\病毒精洗程序\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\泯然\\virus_clean\\build\\病毒精洗程序\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\泯然\\virus_clean\\build\\病毒精洗程序\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\泯然\\virus_clean\\build\\病毒精洗程序\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\泯然\\virus_clean\\build\\病毒精洗程序\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('auto_unzip_and_md5',
   'D:\\泯然\\virus_clean\\virus_clean\\auto_unzip_and_md5.py',
   'PYSOURCE'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ocb.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA256.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Cryptodome\\Math\\_modexp.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Math\\_modexp.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA384.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_des.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_pkcs1_decode.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_MD2.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_aes.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_MD4.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_blowfish.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_cast.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_ed25519.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_curve448.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_BLAKE2b.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_BLAKE2s.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_curve25519.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_cfb.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_ec_ws.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_RIPEMD160.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Cryptodome\\Protocol\\_scrypt.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_eksblowfish.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_ghash_clmul.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA224.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ecb.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_cbc.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_keccak.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_poly1305.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_aesni.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_ARC4.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Cryptodome\\Util\\_cpuid_c.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA1.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_MD5.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_chacha20.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ofb.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_ctr.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_arc2.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_Salsa20.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_SHA512.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Cryptodome\\PublicKey\\_ed448.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Cryptodome\\Hash\\_ghash_portable.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Cryptodome\\Util\\_strxor.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Util\\_strxor.pyd',
   'BINARY'),
  ('Cryptodome\\Cipher\\_raw_des3.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\Cryptodome\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\泯然\\virus_clean\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('base_library.zip',
   'D:\\泯然\\virus_clean\\build\\病毒精洗程序\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
