import os
import time
import zipfile
import hashlib
import datetime
import pymysql
import logging
import subprocess
import shutil
import winreg
import pyzipper
import sys
import argparse
import requests
from typing import List, Dict, Optional
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import traceback
import glob
import struct
import random
import json

# 数据库表名定义，必须在所有用到它的代码之前
TABLE_NAME = 't_virus_clean_1'
QINGBAO_TABLE = 'users_virussample'

# 监控目录配置
PRECISE_WATCH_DIR = r'G:\s1.原始综合样本-malware'  # 精确监控目录
UNZIP_BASE_DIR = 'G:/'
PASSWORDS = [b'', b'infected', b'virus', b'threatbook']
DB_CONFIG = {
    'host': '*************',
    'user': 'root',
    'password': 'Mp2010.18',
    'database': 'qingbao',
    'port': 3306,
    'charset': 'utf8mb4'
}

# VirusTotal API配置
VT_API_KEYS = [
    "****************************************************************",
    "5b40bf6270cbbafc970d46902b8aec5b705545a107a753fca28aca46f5e0224e",
    "2458dbc6972f656ee01ffe64990fefc5c123f6053c105d163b4092ff6430a132",
    "d235eafb0f4fcc728999f8d931a20fe11748356409eb325a7eb140bc0e833459",
    "952c529eadc125272d8a85ad9e957fd35ad9386939b7744d3a0c9818c0e6501a"
]

# VT引擎配置
VT_ENGINES = [
    'huorong', 'Kaspersky', 'ClamAV', 'ESET-NOD32', 'BitDefender', 
    'Rising', 'Symantec', 'McAfeeD', 'TrendMicro', 'Microsoft', 'Avira', 'F-Secure'
]

# MREDR优先引擎
MREDR_PRIORITY_ENGINES = ['huorong', 'Kaspersky', 'ESET-NOD32', 'Rising', 'Symantec', 'TrendMicro']

# VT API相关全局变量
current_api_index = 0
vt_session = requests.Session()
api_usage_count = [0] * len(VT_API_KEYS)

# 自动建表
CREATE_TABLE_SQL = f'''
CREATE TABLE IF NOT EXISTS `{TABLE_NAME}` (
    id INT AUTO_INCREMENT PRIMARY KEY,
    md5 VARCHAR(64) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    src_path TEXT,
    source VARCHAR(64),
    get_date VARCHAR(64),
    date DATE NOT NULL,
    sample_type VARCHAR(64),
    package VARCHAR(255),
    repeat INT DEFAULT 0,
    remark VARCHAR(255),
    vt_huorong VARCHAR(255),
    vt_kaspersky VARCHAR(255),
    vt_eset VARCHAR(255),
    vt_rising VARCHAR(255),
    vt_symantec VARCHAR(255),
    vt_trendmicro VARCHAR(255),
    vt_clamav VARCHAR(255),
    vt_bitdefender VARCHAR(255),
    vt_mcafee VARCHAR(255),
    vt_microsoft VARCHAR(255),
    vt_avira VARCHAR(255),
    vt_fsecure VARCHAR(255),
    vt_first_submit_time DATETIME,
    mredr VARCHAR(255)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
'''

# 配置日志系统 - 同时输出到文件和控制台
def setup_logging():
    # 创建根日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    
    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 文件处理器
    file_handler = logging.FileHandler('auto_unzip_and_md5.log', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

setup_logging()

# 全局变量存储WinRAR路径
WINRAR_PATH = None

# 统计变量
stats = {
    'processed_archives': 0,
    'successful_extractions': 0,
    'failed_extractions': 0,
    'files_processed': 0,
    'files_in_db': 0,
    'db_errors': 0
}

def print_stats():
    """打印统计信息"""
    logging.info("=" * 60)
    logging.info("📊 当前处理统计:")
    logging.info(f"   📦 处理的压缩包: {stats['processed_archives']}")
    logging.info(f"   ✅ 解压成功: {stats['successful_extractions']}")
    logging.info(f"   ❌ 解压失败: {stats['failed_extractions']}")
    logging.info(f"   📄 处理的文件: {stats['files_processed']}")
    logging.info(f"   💾 写入数据库: {stats['files_in_db']}")
    logging.info(f"   ⚠️  数据库错误: {stats['db_errors']}")
    logging.info("=" * 60)

def ensure_table():
    try:
        logging.info("🔧 检查/创建数据库表...")
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            cursor.execute(CREATE_TABLE_SQL)
        conn.commit()
        conn.close()
        logging.info("✅ 数据库表检查/创建成功")
    except Exception as e:
        logging.error(f"❌ 数据库表检查/创建失败: {e}")
        raise

def get_today_folder():
    today = datetime.datetime.now().strftime('%Y%m%d')
    folder = os.path.join(UNZIP_BASE_DIR, today)
    if not os.path.exists(folder):
        os.makedirs(folder)
        logging.info(f"📁 创建今日目录: {folder}")
    return folder

def is_file_stable(filepath, wait=3):
    """检查文件是否写入完成（大小连续几秒不变）"""
    try:
        logging.debug(f"⏳ 检查文件稳定性: {os.path.basename(filepath)}")
        size = os.path.getsize(filepath)
        time.sleep(wait)
        new_size = os.path.getsize(filepath)
        stable = size == new_size
        if stable:
            logging.debug(f"✅ 文件稳定: {os.path.basename(filepath)} ({size} bytes)")
        else:
            logging.warning(f"⚠️  文件还在写入: {os.path.basename(filepath)} ({size} -> {new_size} bytes)")
        return stable
    except Exception as e:
        logging.error(f"❌ 检查文件稳定性失败: {e}")
        return False

def _has_extracted_files(extract_to):
    """检查目录中是否有解压出的文件"""
    try:
        file_count = 0
        for root, dirs, files in os.walk(extract_to):
            file_count += len(files)
        logging.debug(f"📊 解压目录中共有 {file_count} 个文件")
        return file_count > 0
    except:
        return False

def find_winrar():
    # 1. 常见路径
    possible_paths = [
        r"C:\Program Files\WinRAR\WinRAR.exe",
        r"C:\Program Files (x86)\WinRAR\WinRAR.exe"
    ]
    for p in possible_paths:
        if os.path.exists(p):
            return p
    # 2. 注册表查找
    try:
        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\WinRAR.exe")
        value, _ = winreg.QueryValueEx(key, "")
        if os.path.exists(value):
            return value
    except Exception:
        pass
    # 3. PATH 环境变量
    path = shutil.which("WinRAR.exe")
    if path:
        return path
    return None

def extract_with_winrar(archive_path, dest_dir, passwords=None, winrar_path=None):
    import logging
    import subprocess
    import os
    import shutil

    if winrar_path is None:
        winrar_path = find_winrar()
    if not winrar_path or not os.path.exists(winrar_path):
        logging.error(f"未找到 WinRAR.exe，路径: {winrar_path}")
        return False

    if not os.path.exists(dest_dir):
        os.makedirs(dest_dir)

    if not passwords:
        passwords = [b'infected', b'virus', b'threatbook']

    for pwd in passwords:
        # 清理目标目录
        if os.path.exists(dest_dir):
            for f in os.listdir(dest_dir):
                fpath = os.path.join(dest_dir, f)
                if os.path.isfile(fpath):
                    os.remove(fpath)
                elif os.path.isdir(fpath):
                    shutil.rmtree(fpath)
        pwd_str = pwd.decode() if isinstance(pwd, bytes) else pwd
        if pwd_str == '' or pwd_str is None:
            password_arg = '-p""'
            pwd_log = '空密码'
        else:
            password_arg = f'-p"{pwd_str}"'
            pwd_log = pwd_str
        cmd = f'"{winrar_path}" x -y {password_arg} "{archive_path}" "{dest_dir}\\"'
        logging.info(f"尝试WinRAR解压命令: {cmd}")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
            logging.info(f"WinRAR返回码: {result.returncode}")
            logging.info(f"WinRAR输出: {result.stdout}")
            logging.info(f"WinRAR错误: {result.stderr}")
            if result.returncode == 0 or os.listdir(dest_dir):
                logging.info(f"✅ WinRAR解压成功: {archive_path}，密码: {pwd_log}")
                return True
        except Exception as e:
            logging.error(f"WinRAR调用异常: {e}", exc_info=True)
    logging.error(f"❌ 所有密码均无法解压: {archive_path}")
    return False

def extract_archive_recursive(archive_path, extract_to, src_path_chain=None, source=None):
    global stats
    stats['processed_archives'] += 1
    logging.info(f"📦 处理压缩包 #{stats['processed_archives']}: {os.path.basename(archive_path)}")
    if not is_file_stable(archive_path):
        logging.warning(f'⚠️  文件可能未写入完成，跳过: {os.path.basename(archive_path)}')
        return False
    ext = os.path.splitext(archive_path)[1].lower()
    success = False
    # 新建独立解压目录，格式20250722150830_随机数，防止重名
    now_str = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    rand_str = str(random.randint(1000, 9999))
    temp_extract_dir = os.path.join(extract_to, f"{now_str}_{rand_str}")
    os.makedirs(temp_extract_dir, exist_ok=True)
    if src_path_chain is None:
        src_path_chain = [os.path.abspath(archive_path)]
    else:
        src_path_chain = src_path_chain + [os.path.basename(archive_path)]
    if ext == '.zip':
        logging.info(f"🗂️  开始解压ZIP: {os.path.basename(archive_path)}")
        success = extract_zip_with_pyzipper(archive_path, temp_extract_dir, [pwd.decode() if isinstance(pwd, bytes) else pwd for pwd in PASSWORDS])
        if not success:
            logging.error(f'❌ ZIP所有密码均无法解压: {os.path.basename(archive_path)}')
            stats['failed_extractions'] += 1
            return False
    elif ext in ['.rar', '.7z']:
        success = extract_with_winrar(archive_path, temp_extract_dir, PASSWORDS, WINRAR_PATH)
    else:
        logging.warning(f'⚠️  不支持的文件格式: {os.path.basename(archive_path)}')
        return False
    if success:
        stats['successful_extractions'] += 1
        logging.info(f"✅ 解压完成: {os.path.basename(archive_path)}")
        flatten_and_extract_all(temp_extract_dir, archive_path, src_path_chain[0], source)
        for file in os.listdir(temp_extract_dir):
            file_path = os.path.join(temp_extract_dir, file)
            if os.path.isfile(file_path) and not file.lower().endswith(('.zip', '.rar', '.7z')):
                target_path = os.path.join(extract_to, file)
                if os.path.exists(target_path):
                    base, ext = os.path.splitext(file)
                    target_path = os.path.join(extract_to, f"{base}_dup{ext}")
                logging.info(f"[DEBUG] move to main: {file_path} -> {target_path}")
                shutil.move(file_path, target_path)
        try:
            shutil.rmtree(temp_extract_dir)
            logging.debug(f"🗑️  清理临时目录: {temp_extract_dir}")
        except Exception as e:
            logging.warning(f"⚠️  清理临时目录失败: {e}")
    else:
        stats['failed_extractions'] += 1
    return success

def extract_zip_to_temp(zip_path, temp_dir):
    """解压ZIP到临时目录（新版，直接用extract_zip_with_pyzipper）"""
    return extract_zip_with_pyzipper(zip_path, temp_dir, [pwd.decode() if isinstance(pwd, bytes) else pwd for pwd in PASSWORDS])

def extract_rar_to_temp(rar_path, temp_dir):
    """使用WinRAR命令行解压RAR文件到临时目录"""
    global WINRAR_PATH
    if not WINRAR_PATH:
        logging.error('❌ WinRAR路径未设置')
        return False
    
    logging.info(f"🔄 开始解压子RAR: {os.path.basename(rar_path)}")
    
    # 确保目标目录存在
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    
    # 尝试不同密码
    for i, pwd in enumerate(PASSWORDS, 1):
        try:
            pwd_str = pwd.decode() if isinstance(pwd, bytes) else pwd
            logging.info(f"🔑 尝试子RAR密码 {i}/{len(PASSWORDS)}: {pwd_str}")
            
            # WinRAR命令行参数：x=解压保留路径，-y=全部确认，-p=密码
            cmd = [WINRAR_PATH, 'x', '-y', f'-p{pwd_str}', rar_path, temp_dir + '\\']
            
            logging.debug(f'执行子RAR WinRAR命令: {" ".join(cmd)}')
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            logging.debug(f'子RAR WinRAR返回码: {result.returncode}')
            
            # 检查是否有文件被成功解压（即使有警告）
            if result.returncode == 0 or _has_extracted_files(temp_dir):
                # 记录压缩算法不支持的警告，但不视为失败
                if "That compression method is not supported" in result.stdout:
                    logging.warning(f'⚠️  子RAR部分文件压缩算法不支持，但主要内容已解压: {os.path.basename(rar_path)}，密码: {pwd_str}')
                else:
                    logging.info(f'✅ 子RAR解压成功: {os.path.basename(rar_path)}，密码: {pwd_str}')
                return True
            else:
                logging.debug(f'❌ 子RAR密码 {pwd_str} 解压失败，返回码: {result.returncode}')
                continue
                
        except subprocess.TimeoutExpired:
            logging.error(f'⏰ 子RAR解压超时: {os.path.basename(rar_path)}')
            continue
        except Exception as e:
            logging.error(f'❌ 子RAR解压异常: {os.path.basename(rar_path)}, 错误: {e}')
            continue
    
    # 如果所有密码都失败，尝试无密码解压
    try:
        logging.info("🔑 尝试子RAR无密码解压")
        cmd = [WINRAR_PATH, 'x', '-y', rar_path, temp_dir + '\\']
        logging.debug(f'尝试子RAR无密码解压: {" ".join(cmd)}')
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0 or _has_extracted_files(temp_dir):
            if "That compression method is not supported" in result.stdout:
                logging.warning(f'⚠️  子RAR部分文件压缩算法不支持，但主要内容已解压: {os.path.basename(rar_path)}')
            else:
                logging.info(f'✅ 子RAR无密码解压成功: {os.path.basename(rar_path)}')
            return True
        else:
            logging.error(f'❌ 子RAR所有密码均无法解压: {os.path.basename(rar_path)}')
            return False
            
    except Exception as e:
        logging.error(f'❌ 子RAR无密码解压异常: {os.path.basename(rar_path)}, 错误: {e}')
        return False

def recursive_extract_all(dir_path):
    """
    使用while+glob递归查找并解压所有压缩包，避免os.walk删除文件导致的找不到文件错误。
    """
    import logging
    import os
    while True:
        found = False
        for ext in ('*.zip', '*.rar', '*.7z'):
            for file_path in glob.glob(os.path.join(dir_path, '**', ext), recursive=True):
                found = True
                extract_to = os.path.dirname(file_path)
                logging.info(f"[DEBUG] recursive_extract_all: 解压前 {extract_to} 文件列表: {os.listdir(extract_to)}")
                try:
                    if file_path.lower().endswith('.zip'):
                        extract_zip_with_pyzipper(file_path, extract_to, [pwd.decode() if isinstance(pwd, bytes) else pwd for pwd in PASSWORDS])
                    else:
                        extract_with_winrar(file_path, extract_to, PASSWORDS, WINRAR_PATH)
                    logging.info(f'递归解压: {file_path} -> {extract_to}')
                    os.remove(file_path)
                    logging.info(f"[DEBUG] recursive_extract_all: 解压后 {extract_to} 文件列表: {os.listdir(extract_to)}")
                except Exception as e:
                    logging.error(f'递归解压失败: {file_path}, 错误: {e}')
        if not found:
            break

# 修改flatten_and_extract_all，先递归解压所有压缩包，再处理所有文件

def flatten_and_extract_all(source_dir, zip_path, source_dir_path, source):
    import logging
    import shutil
    logging.info(f"[DEBUG] flatten_and_extract_all: 递归解压前 {source_dir} 文件列表: {os.listdir(source_dir)}")
    recursive_extract_all(source_dir)
    logging.info(f"[DEBUG] flatten_and_extract_all: 递归解压后 {source_dir} 文件列表: {os.listdir(source_dir)}")

    # 检查原始zip路径是否来自W站文件夹
    is_from_wstation = False
    if zip_path:
        normalized_zip_path = os.path.normpath(zip_path).replace('\\', '/')
        zip_path_parts = normalized_zip_path.split('/')
        if 'W站' in zip_path_parts:
            is_from_wstation = True
            logging.info(f"🎯 检测到来自W站文件夹的压缩包: {zip_path}")

    # 只move子目录下的非压缩包文件到主目录
    for root, dirs, files in os.walk(source_dir):
        for file in files:
            file_path = os.path.join(root, file)
            if file.lower().endswith((".zip", ".rar", ".7z")):
                continue
            if root != source_dir:
                target_path = os.path.join(source_dir, file)
                if os.path.exists(target_path):
                    base, ext = os.path.splitext(file)
                    target_path = os.path.join(source_dir, f"{base}_dup{ext}")
                logging.info(f"[DEBUG] move: {file_path} -> {target_path}")
                shutil.move(file_path, target_path)
    logging.info(f"[DEBUG] flatten_and_extract_all: 扁平化后 {source_dir} 文件列表: {os.listdir(source_dir)}")
    # 后续处理（如写数据库、分类等）保持不变
    for file in os.listdir(source_dir):
        file_path = os.path.join(source_dir, file)
        if os.path.isfile(file_path) and not file.lower().endswith((".zip", ".rar", ".7z")):
            # 只写入可执行文件 - 如果来自W站文件夹，则强制认为是可执行文件
            if not is_from_wstation and not is_executable_file(file_path):
                logging.info(f"[SKIP] 非可执行文件不写入数据库: {file_path}")
                continue
            elif is_from_wstation and not is_executable_file(file_path):
                logging.info(f"🎯 W站文件夹来源，强制判定为可执行文件: {file_path}")
                # 继续处理，不跳过
            # 新增：支持家族目录
            rel_path = os.path.relpath(file_path, source_dir)
            # 检查是否有家族目录（即rel_path带有路径分隔符）
            if os.sep in rel_path:
                family_dir = rel_path.split(os.sep)[0]
                sample_name = rel_path.split(os.sep)[-1]
                src_path_full = f"{zip_path}\\{family_dir} > {sample_name}"
            else:
                src_path_full = os.path.join(zip_path, rel_path) if zip_path else rel_path
            try:
                md5 = calc_md5(file_path)
                if md5:
                    base_name = os.path.splitext(file)[0]  # 去除后缀
                    logging.info(f"[DEBUG] write_to_db: {file_path} md5={md5}")
                    write_to_db(md5, base_name, os.path.getsize(file_path), datetime.datetime.now().strftime('%Y-%m-%d'), src_path_full, source_dir_path, source)
            except Exception as e:
                logging.error(f"❌ 处理文件失败: {file}, 错误: {e}")

def calc_md5(file_path):
    """计算文件MD5值"""
    try:
        logging.debug(f"🔢 计算MD5: {os.path.basename(file_path)}")
        hash_md5 = hashlib.md5()
        file_size = os.path.getsize(file_path)
        
        with open(file_path, "rb") as f:
            processed = 0
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
                processed += len(chunk)
                
        md5_value = hash_md5.hexdigest()
        logging.debug(f"✅ MD5计算完成: {os.path.basename(file_path)} -> {md5_value}")
        return md5_value
    except Exception as e:
        logging.error(f"❌ MD5计算失败: {os.path.basename(file_path)}, 错误: {e}")
        return None

def switch_vt_api_key():
    """切换到下一个VT API密钥"""
    global current_api_index
    current_api_index = (current_api_index + 1) % len(VT_API_KEYS)

def query_virustotal(md5: str) -> Optional[Dict]:
    """
    查询VirusTotal API
    
    Args:
        md5: MD5哈希值
        
    Returns:
        查询结果字典或None
    """
    global current_api_index, vt_session, api_usage_count
    
    if not VT_API_KEYS:
        return None
    
    max_retries = len(VT_API_KEYS)
    
    for retry in range(max_retries):
        api_key = VT_API_KEYS[current_api_index]
        url = f"https://www.virustotal.com/api/v3/files/{md5}"
        headers = {
            "x-apikey": api_key,
            "User-Agent": "VirusTotal-Scanner/1.0"
        }
        
        try:
            logging.debug(f"🔍 VT查询: {md5} (API #{current_api_index + 1})")
            response = vt_session.get(url, headers=headers, timeout=30)
            api_usage_count[current_api_index] += 1
            
            if response.status_code == 200:
                logging.info(f"   ✅ VT查询成功 (API #{current_api_index + 1})")
                result = response.json()
                switch_vt_api_key()
                return result
            elif response.status_code == 404:
                logging.info(f"   ⚠️  VT文件未找到: {md5}")
                result = {"data": {"attributes": {"last_analysis_results": {}}}}
                switch_vt_api_key()
                return result
            elif response.status_code == 429:
                logging.warning(f"   ⚠️  API #{current_api_index + 1} 达到限制，切换下一个")
                switch_vt_api_key()
                continue
            elif response.status_code == 403:
                logging.error(f"   ❌ API #{current_api_index + 1} 无效，切换下一个")
                switch_vt_api_key()
                continue
            else:
                logging.error(f"   ❌ VT API错误 {response.status_code}: {response.text[:100]}")
                switch_vt_api_key()
                continue
                
        except Exception as e:
            logging.error(f"   ❌ VT查询异常: {e}")
            switch_vt_api_key()
            continue
    
    logging.error(f"   ❌ 所有VT API都失败了")
    return None

def parse_vt_results(md5: str, vt_data: Dict) -> tuple:
    """
    解析VT扫描结果，包括引擎结果、文件类型和病毒家族

    Args:
        md5: MD5哈希
        vt_data: VirusTotal返回的数据

    Returns:
        (engine_results, file_type, virus_family) 元组
    """
    if not vt_data or "data" not in vt_data:
        return {engine: "not_scanned" for engine in VT_ENGINES}, "", ""

    attributes = vt_data["data"]["attributes"]
    vt_results = attributes.get("last_analysis_results", {})

    # 解析引擎结果
    engine_results = {}
    for engine in VT_ENGINES:
        if engine in vt_results:
            engine_data = vt_results[engine]
            category = engine_data.get("category", "")
            result = engine_data.get("result", "")

            if category == "malicious" and result:
                engine_results[engine] = result
            elif category == "undetected":
                engine_results[engine] = "clean"
            elif category == "timeout":
                engine_results[engine] = "timeout"
            elif category == "failure":
                engine_results[engine] = "error"
            else:
                engine_results[engine] = "unknown"
        else:
            engine_results[engine] = "not_scanned"

    # 提取文件类型
    file_type = ""
    type_description = attributes.get("type_description", "")
    magic = attributes.get("magic", "")
    if type_description:
        file_type = type_description
    elif magic:
        file_type = magic

    # 提取病毒家族（从多个引擎的结果中提取最常见的家族名）
    virus_family = extract_virus_family(engine_results)

    logging.debug(f"VT解析结果 - 文件类型: {file_type}, 病毒家族: {virus_family}")

    return engine_results, file_type, virus_family

def extract_virus_family(engine_results: Dict[str, str]) -> str:
    """
    从引擎检测结果中提取病毒家族名称

    Args:
        engine_results: 引擎检测结果字典

    Returns:
        病毒家族名称
    """
    family_candidates = []

    for engine, result in engine_results.items():
        if result and result not in ["clean", "not_scanned", "timeout", "error", "unknown"]:
            # 常见的病毒家族提取模式
            result_lower = result.lower()

            # 提取常见家族名（优先级从高到低）
            families = [
                "trojan", "ransomware", "adware", "spyware", "backdoor",
                "worm", "virus", "rootkit", "keylogger", "botnet",
                "downloader", "dropper", "banker", "stealer", "miner",
                "riskware", "pua", "potentially", "unwanted"
            ]

            for family in families:
                if family in result_lower:
                    family_candidates.append(family.capitalize())
                    break

            # 如果没有匹配到通用家族，尝试提取具体家族名
            if not family_candidates or family_candidates[-1] == "Trojan":
                # 提取Trojan后面的具体家族名
                if "trojan" in result_lower:
                    parts = result.split(".")
                    if len(parts) > 1:
                        specific_family = parts[1].split("/")[0].split("!")[0]
                        if specific_family and len(specific_family) > 2:
                            family_candidates.append(f"Trojan.{specific_family}")

    # 返回最常见的家族名，如果没有则返回空字符串
    if family_candidates:
        # 简单统计，返回最常见的
        from collections import Counter
        most_common = Counter(family_candidates).most_common(1)
        return most_common[0][0] if most_common else ""

    return ""

def calculate_mredr(engine_results: Dict[str, str]) -> str:
    """
    计算mredr综合判定
    
    Args:
        engine_results: 引擎检测结果字典
        
    Returns:
        mredr判定结果
    """
    # 1. 检查优先引擎是否报ransom
    for engine in MREDR_PRIORITY_ENGINES:
        result = engine_results.get(engine, "")
        if result and isinstance(result, str) and result not in ["clean", "not_scanned", "timeout", "error", "unknown"]:
            if "ransom" in result.lower():
                # 替换点号和冒号为下划线
                return result.replace(".", "_").replace(":", "_")
    
    # 2. 检查是否有2家以上报毒
    malicious_results = []
    for engine, result in engine_results.items():
        if result and isinstance(result, str) and result not in ["clean", "not_scanned", "timeout", "error", "unknown"]:
            malicious_results.append(result)
    
    if len(malicious_results) >= 2:
        # 替换点号和冒号为下划线
        return malicious_results[0].replace(".", "_").replace(":", "_")  # 返回第一家的报毒名称
    
    # 3. 其他情况返回clean
    return "clean"

def write_to_db(md5, filename, file_size, date, src_path=None, source_dir=None, source=None):
    """
    写入主数据库，并同步写入情报网数据库
    """
    global stats
    try:
        logging.debug(f"💾 写入数据库: {filename}")
        conn = pymysql.connect(**DB_CONFIG)
        with conn.cursor() as cursor:
            # 先查重 - 只按MD5值查重
            cursor.execute(f"SELECT COUNT(*) FROM `{TABLE_NAME}` WHERE md5=%s", (md5,))
            count = cursor.fetchone()[0]
            if count > 0:
                logging.info(f'⚠️ MD5已存在，跳过写入: {filename} | MD5: {md5}')
                conn.close()
                return None
            else:
                # MD5不存在，先检查大库
                logging.debug(f"🔍 检查大库是否存在相同MD5: {md5}")
                cursor.execute("SELECT COUNT(*) FROM `t_hash_all` WHERE md5=%s", (md5,))
                big_db_count = cursor.fetchone()[0]
                if big_db_count > 0:
                    logging.info(f'🔄 大库中发现重复MD5，跳过VT查询: {filename} | MD5: {md5}')
                    get_date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    package = src_path.split(' > ')[0] if src_path and src_path.strip() else ''
                    sql = f"""INSERT INTO `{TABLE_NAME}`
                             (md5, file_name, file_size, src_path, get_date, date, package, `repeat`, vt_file_type, virus_family)
                             VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
                    cursor.execute(sql, (md5, filename, file_size, src_path, get_date, date, package, 1, "大库重复", ""))
                    logging.info(f'✅ 数据库写入成功(大库重复): {filename} | MD5: {md5} | repeat=1')
                    conn.commit()
                    conn.close()
                    # 同步写入情报网（无VT信息），collection_channel用source
                    write_to_qingbao_db(md5, filename, file_size, date, src_path, None, source)
                    stats['files_in_db'] += 1
                    return True
                else:
                    logging.info(f'🔍 大库中未发现MD5，开始VT查询: {filename} | MD5: {md5}')
                    vt_data = query_virustotal(md5)
                    engine_results, vt_file_type, virus_family = parse_vt_results(md5, vt_data)
                    mredr_result = calculate_mredr(engine_results)

                    # 记录提取的文件信息
                    if vt_file_type or virus_family:
                        logging.info(f'📋 VT文件信息: {filename} | 类型: {vt_file_type} | 家族: {virus_family}')
                    vt_first_submit_time = None
                    if vt_data and "data" in vt_data:
                        first_submission_date = vt_data["data"]["attributes"].get("first_submission_date")
                        if first_submission_date:
                            vt_first_submit_time = datetime.datetime.fromtimestamp(first_submission_date)
                    get_date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    package = src_path.split(' > ')[0] if src_path and src_path.strip() else ''
                    # source参数直接传递，添加文件类型和病毒家族字段
                    sql = f"""INSERT INTO `{TABLE_NAME}`
                             (md5, file_name, file_size, src_path, source, get_date, date, package, `repeat`,
                              vt_huorong, vt_kaspersky, vt_eset, vt_rising, vt_symantec, vt_trendmicro,
                              vt_clamav, vt_bitdefender, vt_mcafee, vt_microsoft, vt_avira, vt_fsecure,
                              vt_first_submit_time, mredr, vt_file_type, virus_family)
                             VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
                    cursor.execute(sql, (
                        md5, filename, file_size, src_path, source, get_date, date, package, 0,
                        engine_results.get('huorong', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('Kaspersky', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('ESET-NOD32', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('Rising', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('Symantec', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('TrendMicro', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('ClamAV', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('BitDefender', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('McAfeeD', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('Microsoft', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('Avira', '').replace(".", "_").replace(":", "_"),
                        engine_results.get('F-Secure', '').replace(".", "_").replace(":", "_"),
                        vt_first_submit_time, mredr_result, vt_file_type, virus_family
                    ))
                    conn.commit()
                    conn.close()
                    # 同步写入情报网（含VT首次提交时间），collection_channel用source
                    write_to_qingbao_db(md5, filename, file_size, date, src_path, vt_first_submit_time, source)
                    stats['files_in_db'] += 1
                    return True
    except Exception as e:
        stats['db_errors'] += 1
        logging.error(f'❌ 数据库写入失败: {filename}, 错误详情: {e}')
        logging.error(f'❌ 错误类型: {type(e).__name__}')
        if hasattr(e, 'args') and len(e.args) > 1:
            logging.error(f'❌ SQL错误码: {e.args[0]}, 错误信息: {e.args[1]}')
    return None

def auto_set_winrar_tool():
    """自动检测WinRAR路径"""
    global WINRAR_PATH
    logging.info("🔍 正在检测WinRAR工具...")
    
    possible_paths = [
        r'C:\Program Files\WinRAR\WinRAR.exe',
        r'C:\Program Files (x86)\WinRAR\WinRAR.exe',
        r'C:\WinRAR\WinRAR.exe',
        os.path.join(os.path.dirname(os.path.abspath(__file__)), 'WinRAR.exe')
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            WINRAR_PATH = path
            logging.info(f'✅ 自动检测到WinRAR工具: {path}')
            return True
    
    logging.error('❌ 未能自动找到WinRAR.exe，请手动安装WinRAR或将其路径加入系统PATH')
    return False

def write_malware_to_hash_all():
    """
    将mredr报毒的样本写入大库t_hash_all中
    """
    try:
        logging.info("🔍 开始将mredr报毒的样本写入大库t_hash_all...")
        
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        today_date = datetime.datetime.now().strftime('%Y-%m-%d')
        
        with conn.cursor() as cursor:
            # 查询今天mredr报毒的样本（不是clean和not_scanned）
            sql = """
            SELECT md5, file_name, file_size, mredr
            FROM `{}` 
            WHERE date = %s AND `repeat` = 0 AND mredr IS NOT NULL 
            AND mredr != 'clean' AND mredr != 'not_scanned' AND mredr != ''
            """.format(TABLE_NAME)
            cursor.execute(sql, (today_date,))
            malware_samples = cursor.fetchall()
            
            if not malware_samples:
                logging.info("ℹ️  今天没有找到mredr报毒的样本")
                conn.close()
                return
            
            malware_count = 0
            for row in malware_samples:
                md5, file_name, file_size, mredr = row
                
                # 检查大库中是否已存在此MD5
                cursor.execute("SELECT COUNT(*) FROM `t_hash_all` WHERE md5=%s", (md5,))
                exists = cursor.fetchone()[0]
                
                if exists > 0:
                    logging.debug("⚠️  大库中已存在MD5，跳过: %s | MD5: %s", 
                                 str(file_name).replace('%', '%%'), 
                                 str(md5).replace('%', '%%'))
                    continue
                
                # 插入到大库（需要手动生成id）
                try:
                    # 获取当前最大id
                    cursor.execute("SELECT COALESCE(MAX(id), 0) + 1 FROM `t_hash_all`")
                    next_id = cursor.fetchone()[0]
                    
                    # 处理mredr字段，避免特殊字符导致的格式化错误
                    safe_mredr = str(mredr).replace('%', '%%') if mredr else ''
                    
                    # 确保file_size是整数类型
                    file_size_int = int(file_size) if file_size else 0
                    
                    insert_sql = """
                    INSERT INTO `t_hash_all` (id, file_size, md5, mredr)
                    VALUES (%s, %s, %s, %s)
                    """
                    cursor.execute(insert_sql, (next_id, file_size_int, md5, safe_mredr))
                    malware_count += 1
                    logging.debug("✅ 写入大库: %s | MD5: %s | MREDR: %s | ID: %s", 
                                 str(file_name).replace('%', '%%'), 
                                 str(md5).replace('%', '%%'), 
                                 safe_mredr, next_id)
                except Exception as e:
                    logging.error("❌ 写入大库失败: %s | MD5: %s, 错误: %s", 
                                 str(file_name).replace('%', '%%'), 
                                 str(md5).replace('%', '%%'), 
                                 str(e).replace('%', '%%'))
            
            conn.commit()
            logging.info(f"✅ 大库写入完成: 共写入 {malware_count} 个mredr报毒样本")

        conn.close()
        return malware_count

    except Exception as e:
        logging.error(f"❌ 写入大库失败: {e}")
        return 0

def write_ransom_to_ransom_db():
    """
    将mredr报ransom的样本写入小库t_hash_ransom中
    """
    try:
        logging.info("🔍 开始将mredr报ransom的样本写入小库t_hash_ransom...")
        
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        today_date = datetime.datetime.now().strftime('%Y-%m-%d')
        
        with conn.cursor() as cursor:
            # 查询今天mredr报ransom的样本
            sql = """
            SELECT md5, file_name, file_size, mredr
            FROM `{}` 
            WHERE date = %s AND `repeat` = 0 AND mredr LIKE %s
            """.format(TABLE_NAME)
            cursor.execute(sql, (today_date, '%ransom%'))
            ransom_samples = cursor.fetchall()
            
            if not ransom_samples:
                logging.info("ℹ️  今天没有找到mredr报ransom的样本")
                conn.close()
                return 0
            
            ransom_count = 0
            for row in ransom_samples:
                md5, file_name, file_size, mredr = row
                
                # 检查小库中是否已存在此MD5
                cursor.execute("SELECT COUNT(*) FROM `t_hash_ransom` WHERE md5=%s", (md5,))
                exists = cursor.fetchone()[0]
                
                if exists > 0:
                    logging.debug("⚠️  小库中已存在MD5，跳过: %s | MD5: %s", 
                                 str(file_name).replace('%', '%%'), 
                                 str(md5).replace('%', '%%'))
                    continue
                
                # 插入到小库（需要手动生成id）
                try:
                    # 获取当前最大id
                    cursor.execute("SELECT COALESCE(MAX(id), 0) + 1 FROM `t_hash_ransom`")
                    next_id = cursor.fetchone()[0]
                    
                    # 处理mredr字段，避免特殊字符导致的格式化错误
                    safe_mredr = str(mredr).replace('%', '%%') if mredr else ''
                    
                    # 确保file_size是整数类型
                    file_size_int = int(file_size) if file_size else 0
                    
                    insert_sql = """
                    INSERT INTO `t_hash_ransom` (id, file_size, md5, mredr)
                    VALUES (%s, %s, %s, %s)
                    """
                    cursor.execute(insert_sql, (next_id, file_size_int, md5, safe_mredr))
                    ransom_count += 1
                    logging.debug("✅ 写入小库: %s | MD5: %s | MREDR: %s | ID: %s", 
                                 str(file_name).replace('%', '%%'), 
                                 str(md5).replace('%', '%%'), 
                                 safe_mredr, next_id)
                except Exception as e:
                    logging.error("❌ 写入小库失败: %s | MD5: %s, 错误: %s", 
                                 str(file_name).replace('%', '%%'), 
                                 str(md5).replace('%', '%%'), 
                                 str(e).replace('%', '%%'))
            
            conn.commit()
            logging.info("✅ 小库写入完成: 共写入 %d 个mredr报ransom样本", ransom_count)

        conn.close()
        return ransom_count

    except Exception as e:
        logging.error("❌ 写入小库失败: %s", str(e).replace('%', '%%'))
        return 0

def generate_excel_report(target_dir: str) -> str:
    """
    生成Excel报告，统计12个引擎的检出率
    
    Args:
        target_dir: 解压目标目录
        
    Returns:
        生成的Excel文件路径
    """
    try:
        logging.info("📊 开始生成Excel检出率报告...")
        
        # 连接数据库查询今天的数据
        conn = pymysql.connect(**DB_CONFIG)
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        
        with conn.cursor() as cursor:
            # 查询今天的所有样本数据
            sql = f"""
            SELECT vt_huorong, vt_kaspersky, vt_eset, vt_rising, vt_symantec, vt_trendmicro,
                   vt_clamav, vt_bitdefender, vt_mcafee, vt_microsoft, vt_avira, vt_fsecure,
                   mredr, file_name, md5
            FROM `{TABLE_NAME}` 
            WHERE date = %s AND `repeat` = 0
            """
            cursor.execute(sql, (today,))
            results = cursor.fetchall()
        
        conn.close()
        
        if not results:
            logging.warning("⚠️  今天没有找到新样本数据，无法生成报告")
            return None
        
        total_samples = len(results)
        logging.info(f"📊 找到 {total_samples} 个今日新样本")
        
        # 统计各引擎检出情况
        engine_stats = {}
        engine_names = ['huorong', 'kaspersky', 'eset', 'rising', 'symantec', 'trendmicro',
                       'clamav', 'bitdefender', 'mcafee', 'microsoft', 'avira', 'fsecure']
        
        for i, engine in enumerate(engine_names):
            detected = 0
            clean = 0
            not_scanned = 0
            other = 0
            
            for row in results:
                result = row[i] if row[i] else ""
                if result == "clean":
                    clean += 1
                elif result == "not_scanned":
                    not_scanned += 1
                elif result in ["", "unknown", "timeout", "error"]:
                    other += 1
                else:
                    detected += 1
            
            detection_rate = (detected / total_samples) * 100 if total_samples > 0 else 0
            
            engine_stats[engine] = {
                'detected': detected,
                'clean': clean,
                'not_scanned': not_scanned,
                'other': other,
                'detection_rate': detection_rate
            }
        
        # 创建Excel工作簿
        excel_filename = f"virus_detection_report_{today}.xlsx"
        excel_path = os.path.join(target_dir, excel_filename)
        
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "检出率统计"
        
        # 设置表头
        headers = ['引擎名称', '检出样本数', '清洁样本数', '未扫描样本数', '其他状态样本数', '检出率(%)']
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # 填充数据
        row = 2
        for engine, stats in engine_stats.items():
            worksheet.cell(row=row, column=1, value=engine)
            worksheet.cell(row=row, column=2, value=stats['detected'])
            worksheet.cell(row=row, column=3, value=stats['clean'])
            worksheet.cell(row=row, column=4, value=stats['not_scanned'])
            worksheet.cell(row=row, column=5, value=stats['other'])
            worksheet.cell(row=row, column=6, value=round(stats['detection_rate'], 2))
            row += 1
        
        # 添加总计行
        total_row = row
        worksheet.cell(row=total_row, column=1, value="总计")
        worksheet.cell(row=total_row, column=2, value=sum(engine_stat['detected'] for engine_stat in engine_stats.values()))
        worksheet.cell(row=total_row, column=3, value=sum(engine_stat['clean'] for engine_stat in engine_stats.values()))
        worksheet.cell(row=total_row, column=4, value=sum(engine_stat['not_scanned'] for engine_stat in engine_stats.values()))
        worksheet.cell(row=total_row, column=5, value=sum(engine_stat['other'] for engine_stat in engine_stats.values()))
        worksheet.cell(row=total_row, column=6, value=f"样本总数: {total_samples}")
        
        # 设置总计行样式
        for col in range(1, 7):
            cell = worksheet.cell(row=total_row, column=col)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="FFFFCC", end_color="FFFFCC", fill_type="solid")
        
        # 自动调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # 保存Excel文件
        workbook.save(excel_path)
        
        logging.info(f"✅ Excel报告生成成功: {excel_path}")
        return excel_path
        
    except Exception as e:
        logging.error(f"❌ 生成Excel报告失败: {e}")
        return None

def classify_samples_by_detection(target_dir: str):
    """
    根据检测结果分类样本文件
    - Clean样本：mredr为clean的样本
    - Not_scan样本：12个引擎中有6个以上报not_scan且mredr报clean的样本
    - 确保clean样本和not_scan样本不重复
    
    Args:
        target_dir: 解压目标目录
    """
    try:
        logging.info("📁 开始分类样本文件...")
        
        # 创建分类文件夹
        today = datetime.datetime.now().strftime('%Y%m%d')
        classify_base_dir = os.path.join(os.path.dirname(target_dir), today)
        clean_dir = os.path.join(classify_base_dir, "clean_samples")
        not_scan_dir = os.path.join(classify_base_dir, "not_scan_samples")
        
        os.makedirs(clean_dir, exist_ok=True)
        os.makedirs(not_scan_dir, exist_ok=True)
        
        logging.info(f"📁 创建分类目录: {classify_base_dir}")
        logging.info(f"   - Clean样本目录: {clean_dir}")
        logging.info(f"   - Not_scan样本目录: {not_scan_dir}")
        
        # 连接数据库查询今天的数据
        conn = pymysql.connect(**DB_CONFIG)
        today_date = datetime.datetime.now().strftime('%Y-%m-%d')
        
        with conn.cursor() as cursor:
            # 查询今天的所有样本数据
            sql = f"""
            SELECT file_name, md5, mredr,
                   vt_huorong, vt_kaspersky, vt_eset, vt_rising, vt_symantec, vt_trendmicro,
                   vt_clamav, vt_bitdefender, vt_mcafee, vt_microsoft, vt_avira, vt_fsecure
            FROM `{TABLE_NAME}` 
            WHERE date = %s AND `repeat` = 0
            """
            cursor.execute(sql, (today_date,))
            results = cursor.fetchall()
        
        conn.close()
        
        if not results:
            logging.warning("⚠️  今天没有找到新样本数据")
            return
        
        clean_count = 0
        not_scan_count = 0
        processed_files = set()  # 记录已处理的文件，避免重复
        
        for row in results:
            file_name = row[0]
            md5 = row[1]
            mredr = row[2]
            engine_results = row[3:15]  # 12个引擎结果
            
            # 检查文件是否存在于解压目录
            source_file = os.path.join(target_dir, file_name)
            if not os.path.exists(source_file):
                logging.debug(f"⚠️  文件不存在，跳过: {file_name}")
                continue
            
            # 避免重复处理同一个文件
            if file_name in processed_files:
                logging.debug(f"⚠️  文件已处理，跳过: {file_name}")
                continue
            
            # 统计not_scan引擎数量
            not_scan_count_engines = sum(1 for result in engine_results if result == "not_scanned" or result == "")
            
            # 判断是否为not_scan样本（12个引擎中有6个以上报not_scan且mredr报clean）
            if not_scan_count_engines >= 6 and mredr == "clean":
                dest_file = os.path.join(not_scan_dir, file_name)
                # 如果目标文件已存在，添加MD5前缀避免冲突
                if os.path.exists(dest_file):
                    name, ext = os.path.splitext(file_name)
                    dest_file = os.path.join(not_scan_dir, f"{md5[:8]}_{name}{ext}")
                
                try:
                    shutil.copy2(source_file, dest_file)
                    not_scan_count += 1
                    processed_files.add(file_name)
                    logging.debug(f"📄 Not_scan样本: {file_name} -> {os.path.basename(dest_file)} (not_scan引擎数: {not_scan_count_engines})")
                except Exception as e:
                    logging.error(f"❌ 复制not_scan样本失败: {file_name}, 错误: {e}")
                continue
            
            # 判断是否为clean样本（mredr为clean且未被归类为not_scan）
            if mredr == "clean":
                dest_file = os.path.join(clean_dir, file_name)
                # 如果目标文件已存在，添加MD5前缀避免冲突
                if os.path.exists(dest_file):
                    name, ext = os.path.splitext(file_name)
                    dest_file = os.path.join(clean_dir, f"{md5[:8]}_{name}{ext}")
                
                try:
                    shutil.copy2(source_file, dest_file)
                    clean_count += 1
                    processed_files.add(file_name)
                    logging.debug(f"📄 Clean样本: {file_name} -> {os.path.basename(dest_file)}")
                except Exception as e:
                    logging.error(f"❌ 复制clean样本失败: {file_name}, 错误: {e}")
        
        logging.info(f"✅ 样本分类完成:")
        logging.info(f"   - Clean样本: {clean_count} 个")
        logging.info(f"   - Not_scan样本: {not_scan_count} 个 (6个以上引擎报not_scan且mredr为clean)")
        logging.info(f"   - 分类目录: {classify_base_dir}")

        return clean_count, not_scan_count

    except Exception as e:
        logging.error(f"❌ 样本分类失败: {e}")
        return 0, 0

def find_archive_files(directory, processed_files):
    """
    递归搜索目录中的压缩包文件
    """
    archive_files = []
    try:
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith(('.zip', '.rar', '.7z')):
                    file_path = os.path.join(root, file)
                    if file_path not in processed_files:
                        archive_files.append((file_path, root))
    except Exception as e:
        logging.error(f"❌ 搜索目录失败 {directory}: {e}")
    return archive_files

def main_loop():
    logging.info('🚀 程序启动')
    logging.info(f"📁 精确监控目录: {PRECISE_WATCH_DIR}")
    logging.info(f"📁 解压目标: {UNZIP_BASE_DIR}")
    logging.info("🎯 解压模式: 完全扁平化 (所有文件都在同一目录)")
    
    # 检测WinRAR
    if not auto_set_winrar_tool():
        logging.error('❌ WinRAR未找到，程序退出')
        return
    
    # ensure_table()  # 注释掉自动建表，使用现有数据库表
    processed = set()
    loop_count = 0
    
    logging.info("🔄 开始监控循环...")
    
    while True:
        try:
            loop_count += 1
            logging.debug(f'⏱️  监控循环 #{loop_count}')
            
            # 获取所有监控目录中的压缩包
            all_watch_dirs = [PRECISE_WATCH_DIR]
            new_files_found = False
            
            for watch_dir in all_watch_dirs:
                if os.path.exists(watch_dir):
                    logging.debug(f'📂 检查目录: {watch_dir}')
                    # 递归搜索压缩包
                    archive_files = find_archive_files(watch_dir, processed)
                    if archive_files:
                        logging.debug(f'📂 在 {watch_dir} 中发现 {len(archive_files)} 个新压缩包')
                        for zip_path, source_dir in archive_files:
                            new_files_found = True
                            now_str = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
                            fname = os.path.basename(zip_path)
                            extract_dir_name = f"{now_str}_{fname}"
                            extract_to = os.path.join(UNZIP_BASE_DIR, extract_dir_name)
                            os.makedirs(extract_to, exist_ok=True)
                            logging.info("=" * 80)
                            logging.info(f'🆕 检测到新压缩包: {fname} (来自: {source_dir})')
                            logging.info(f'📁 解压目标目录: {extract_to}')

                            # 重置stats中的文件处理计数（避免累积效应）
                            stats['files_in_db'] = 0
                            logging.debug(f"🔄 重置stats['files_in_db']为0，开始新的处理周期")
                            # 日志统计变量，所有key都初始化
                            log_stats = {
                                'total_extracted': 0,
                                'executable_count': 0,
                                f'written_{TABLE_NAME}_数量': 0,
                                'written_users_virussample': 0,
                                'written_t_hash_all': 0,
                                'written_t_hash_ransom': 0,
                                'clean_samples': 0,
                                'not_scan_samples': 0,
                                'non_executable_count': 0,
                                'failures': []
                            }
                            clean_result = 'fail'
                            try:
                                if extract_archive_recursive(zip_path, extract_to):
                                    processed.add(zip_path)
                                    # 等待一秒确保所有文件操作完成
                                    time.sleep(1)
                                    logging.info(f"📊 开始处理解压文件...")
                                    # --- 统计解压出来的文件数 ---
                                    extracted_files = [f for f in os.listdir(extract_to) if os.path.isfile(os.path.join(extract_to, f)) and not f.lower().endswith((".zip", ".rar", ".7z"))]
                                    log_stats['total_extracted'] = len(extracted_files)

                                    # --- 检查是否来自W站文件夹 ---
                                    is_from_wstation = False
                                    if zip_path:
                                        normalized_zip_path = os.path.normpath(zip_path).replace('\\', '/')
                                        zip_path_parts = normalized_zip_path.split('/')
                                        if 'W站' in zip_path_parts:
                                            is_from_wstation = True

                                    # --- 统计可执行文件数量（考虑W站特殊规则）---
                                    exec_count = 0
                                    non_exec_count = 0
                                    for f in extracted_files:
                                        fpath = os.path.join(extract_to, f)
                                        is_exec = is_executable_file(fpath)
                                        # 如果来自W站，强制认为是可执行文件
                                        if is_from_wstation and not is_exec:
                                            is_exec = True

                                        if is_exec:
                                            exec_count += 1
                                        else:
                                            non_exec_count += 1

                                    log_stats['executable_count'] = exec_count
                                    log_stats['non_executable_count'] = non_exec_count

                                    # 记录处理前的统计数据
                                    before_files_in_db = stats.get('files_in_db', 0)

                                    # --- 处理解压文件 ---
                                    flatten_and_extract_all(extract_to, zip_path, watch_dir, 'W站')

                                    print_stats()
                                    logging.info("📊 开始生成报告和分类样本...")

                                    # 执行各种写入操作
                                    excel_path = generate_excel_report(extract_to)
                                    if excel_path:
                                        logging.info(f"✅ Excel报告已生成: {excel_path}")

                                    # 获取实际的分类和写入数量
                                    clean_count, not_scan_count = classify_samples_by_detection(extract_to)
                                    hash_all_count = write_malware_to_hash_all()
                                    hash_ransom_count = write_ransom_to_ransom_db()
                                    # 注意：情报网表是在write_to_db中同步写入的，不需要单独调用

                                    # --- 统计实际写入数量（直接查询数据库，避免stats变量问题）---
                                    main_table_written = count_recent_writes_in_main_table()
                                    log_stats[f'written_{TABLE_NAME}_数量'] = main_table_written
                                    # 情报网表的写入数量需要单独统计
                                    qingbao_written = count_recent_writes_in_qingbao_table()
                                    log_stats['written_users_virussample'] = qingbao_written
                                    log_stats['written_t_hash_all'] = hash_all_count if hash_all_count else 0
                                    log_stats['written_t_hash_ransom'] = hash_ransom_count if hash_ransom_count else 0
                                    log_stats['clean_samples'] = clean_count if clean_count else 0
                                    log_stats['not_scan_samples'] = not_scan_count if not_scan_count else 0

                                    # --- 统计查重数量 ---
                                    duplicate_count = count_duplicate_in_main_table(extract_to)
                                    log_stats['duplicate_in_main_table'] = duplicate_count

                                    # 日志写入前，保证所有key都存在
                                    for k in ['total_extracted','executable_count',f'written_{TABLE_NAME}_数量','written_users_virussample','written_t_hash_all','written_t_hash_ransom','clean_samples','not_scan_samples','non_executable_count','duplicate_in_main_table','failures']:
                                        if k not in log_stats:
                                            log_stats[k] = 0 if k != 'failures' else []

                                    # 处理成功，设置clean_result为success
                                    clean_result = 'success'
                                else:
                                    logging.error(f'❌ 解压失败: {fname}')
                                    log_stats['fail_count'] = 1
                                    log_stats['failures'].append({'file': fname, 'reason': '解压失败'})
                                    clean_result = 'fail'
                            except Exception as e:
                                logging.error(f'❌ 处理压缩包异常: {fname}, 错误: {e}')
                                log_stats['failures'].append({'file': fname, 'reason': str(e)})
                                # 补全所有key，防止日志写入报错
                                for k in ['total_extracted','executable_count',f'written_{TABLE_NAME}_数量','written_users_virussample','written_t_hash_all','written_t_hash_ransom','clean_samples','not_scan_samples','non_executable_count','duplicate_in_main_table','failures']:
                                    if k not in log_stats:
                                        log_stats[k] = 0 if k != 'failures' else []
                                clean_result = 'fail'
                            # 日志写入
                            write_virus_clean_log(fname, zip_path, clean_result, log_stats)
                else:
                    logging.warning(f"⚠️  监控目录不存在: {watch_dir}")
            
            if not new_files_found and loop_count % 10 == 0:  # 每10次循环显示一次状态
                logging.info(f"😴 等待新文件... (循环 #{loop_count})")
                print_stats()
            
            time.sleep(60)
        except KeyboardInterrupt:
            logging.info("⏹️  用户中断程序")
            print_stats()
            break
        except Exception as e:
            logging.error(f'❌ 主循环异常: {e}')
            time.sleep(60)
            
            if not new_files_found and loop_count % 10 == 0:  # 每10次循环显示一次状态
                logging.info(f"😴 等待新文件... (循环 #{loop_count})")
                print_stats()
            
            time.sleep(60)
        except KeyboardInterrupt:
            logging.info("⏹️  用户中断程序")
            print_stats()
            break
        except Exception as e:
            logging.error(f'❌ 主循环异常: {e}')
            time.sleep(60)

def extract_zip_with_pyzipper(zip_path, extract_to, passwords=None):
    if not os.path.exists(extract_to):
        os.makedirs(extract_to)
    tried = set()
    if not passwords:
        passwords = [None]
    for pwd in passwords:
        tried.add(pwd)
        try:
            with pyzipper.AESZipFile(zip_path) as zf:
                if pwd:
                    zf.extractall(path=extract_to, pwd=pwd.encode())
                else:
                    zf.extractall(path=extract_to)
            logging.info(f"✅ 解压成功: {zip_path}，密码: {pwd}")
            return True
        except RuntimeError as e:
            logging.warning(f"❌ 密码错误: {pwd}，尝试下一个")
            continue
        except pyzipper.BadZipFile:
            logging.error(f"❌ 不是有效的 zip 文件: {zip_path}")
            break
        except Exception as e:
            logging.error(f"❌ 解压异常: {e}")
            continue
    if None not in tried:
        try:
            with pyzipper.AESZipFile(zip_path) as zf:
                zf.extractall(path=extract_to)
            logging.info(f"✅ 无密码解压成功: {zip_path}")
            return True
        except Exception as e:
            logging.error(f"❌ 无密码也无法解压: {zip_path}，错误: {e}")
    logging.error(f"❌ 所有密码均无法解压: {zip_path}")
    return False

def get_file_type(filename):
    """
    根据文件名检测文件类型
    """
    if not filename:
        return '未知'
    
    # 获取文件扩展名
    ext = os.path.splitext(filename)[1].lower()
    
    # 常见文件类型映射
    file_types = {
        # 可执行文件
        '.exe': 'PE文件',
        '.dll': '动态链接库',
        '.sys': '系统文件',
        '.scr': '屏幕保护程序',
        '.com': 'COM文件',
        '.bat': '批处理文件',
        '.cmd': '命令文件',
        '.ps1': 'PowerShell脚本',
        '.vbs': 'VBScript脚本',
        '.js': 'JavaScript脚本',
        '.jar': 'Java归档文件',
        '.msi': '安装程序',
        '.msu': '更新程序',
        
        # 文档文件
        '.doc': 'Word文档',
        '.docx': 'Word文档',
        '.xls': 'Excel表格',
        '.xlsx': 'Excel表格',
        '.ppt': 'PowerPoint演示',
        '.pptx': 'PowerPoint演示',
        '.pdf': 'PDF文档',
        '.rtf': '富文本格式',
        
        # 压缩文件
        '.zip': '压缩文件',
        '.rar': '压缩文件',
        '.7z': '压缩文件',
        '.tar': '压缩文件',
        '.gz': '压缩文件',
        
        # 媒体文件
        '.mp3': '音频文件',
        '.mp4': '视频文件',
        '.avi': '视频文件',
        '.jpg': '图片文件',
        '.png': '图片文件',
        '.gif': '图片文件',
        
        # 其他
        '.txt': '文本文件',
        '.log': '日志文件',
        '.ini': '配置文件',
        '.cfg': '配置文件',
        '.xml': 'XML文件',
        '.json': 'JSON文件',
        '.html': 'HTML文件',
        '.htm': 'HTML文件',
        '.css': 'CSS文件',
        '.php': 'PHP脚本',
        '.asp': 'ASP脚本',
        '.aspx': 'ASP.NET页面',
        '.jsp': 'JSP页面',
        '.py': 'Python脚本',
        '.pl': 'Perl脚本',
        '.sh': 'Shell脚本',
        '.sql': 'SQL脚本',
        '.bak': '备份文件',
        '.tmp': '临时文件',
        '.dat': '数据文件',
        '.bin': '二进制文件',
        '.hex': '十六进制文件',
        '.iso': '光盘镜像',
        '.img': '磁盘镜像',
        '.vhd': '虚拟硬盘',
        '.vmdk': '虚拟机磁盘',
        '.ova': '虚拟机模板',
        '.ovf': '虚拟机描述',
    }
    
    return file_types.get(ext, '未知文件类型')

def write_to_qingbao_db(md5, filename, file_size, date, src_path, vt_first_submit_time, collection_channel=None):
    """
    写入情报网数据库（users_virussample），collection_channel字段用主表source字段。
    """
    try:
        qingbao_db_connection = pymysql.connect(**DB_CONFIG)
        cursor = qingbao_db_connection.cursor()
        # 1. 检查同名且md5_is_auto=1的记录，或同名且md5_is_auto=0但file_size为0的记录
        cursor.execute(f"SELECT id, md5_is_auto, file_size FROM `{QINGBAO_TABLE}` WHERE file_name=%s", (filename,))
        update_record = cursor.fetchone()
        if update_record:
            update_id, md5_is_auto_val, old_file_size = update_record
            if md5_is_auto_val == 1 or (md5_is_auto_val == 0 and (old_file_size is None or old_file_size == 0)):
                cursor.execute(f"UPDATE `{QINGBAO_TABLE}` SET md5=%s, md5_is_auto=0, file_size=%s WHERE id=%s", (md5, file_size, update_id))
                qingbao_db_connection.commit()
                logging.info(f"🔄 已更新同名记录: file_name={filename}, 新md5={md5}, 新file_size={file_size}")
                cursor.close()
                qingbao_db_connection.close()
                return True
        # 2. 查重：md5或file_name有一个重复就跳过
        cursor.execute(f"SELECT id FROM `{QINGBAO_TABLE}` WHERE md5=%s OR file_name=%s", (md5, filename))
        exist_record = cursor.fetchone()
        if exist_record:
            logging.debug(f"📝 样本 {filename} (MD5: {md5}) 或文件名已存在于情报网数据库，跳过")
            cursor.close()
            qingbao_db_connection.close()
            return False  # 返回False表示没有新增记录
        # 获取当前最大序号
        cursor.execute(f"SELECT COUNT(*) FROM `{QINGBAO_TABLE}`")
        total_count = cursor.fetchone()[0]
        if total_count == 0:
            next_index = 1
        else:
            cursor.execute(f"SELECT MIN(excel_index), MAX(excel_index) FROM `{QINGBAO_TABLE}`")
            min_index, max_index = cursor.fetchone()
            if min_index == 1 and max_index == total_count:
                next_index = max_index + 1
            else:
                logging.info("🔄 检测到序号不连续，开始重新整理序号...")
                cursor.execute(f"SET @row_number = 0")
                cursor.execute(f"""
                    UPDATE `{QINGBAO_TABLE}` 
                    SET excel_index = (@row_number:=@row_number + 1) 
                    ORDER BY excel_index, id
                """)
                next_index = total_count + 1
                logging.info(f"✅ 序号重新整理完成，下一个序号: {next_index}")
        # 字段赋值
        virus_family = None
        test_status = '未测试'
        last_test_date = None
        risk_level = '未知'
        alert_status = '未触及'
        platform = 'Windows'
        created_at = datetime.datetime.now()
        updated_at = created_at
        file_type = get_file_type(filename)
        if src_path:
            path_parts = src_path.split('\\')
            if len(path_parts) > 1:
                virus_family = path_parts[1]
        file_path = src_path if src_path and len(src_path) <= 100 else (src_path[:100] if src_path else None)
        upload_date = created_at
        remark = f"来源: {src_path}"
        md5_is_auto = 0
        # collection_channel用主表source字段
        insert_sql = f'''
        INSERT INTO `{QINGBAO_TABLE}` (
            file_name, md5, file_type, file_size, upload_date, last_test_date, virus_family, collection_channel, test_status, risk_level, file_path, remark, created_at, updated_at, platform, alert_status, md5_is_auto, excel_index, first_submit_time
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        '''
        cursor.execute(insert_sql, (
            filename, md5, file_type, file_size, upload_date, last_test_date, virus_family, collection_channel, test_status, risk_level, file_path, remark, created_at, updated_at, platform, alert_status, md5_is_auto, next_index, vt_first_submit_time
        ))
        qingbao_db_connection.commit()
        cursor.close()
        qingbao_db_connection.close()
        logging.info(f"✅ 样本 {md5} 已写入情报网数据库 (序号: {next_index}, 类型: {file_type})")
        return True
    except Exception as e:
        logging.error(f"❌ 写入情报网数据库失败 {md5}: {e}")
        logging.error(traceback.format_exc())
        return False

def count_recent_writes_in_qingbao_table():
    """
    统计最近写入情报网表的记录数量（最近5分钟内）
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 查询最近5分钟内写入的记录数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM `{QINGBAO_TABLE}`
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        """)

        recent_count = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        logging.info(f"📊 最近5分钟内写入情报网表记录数: {recent_count}")
        return recent_count

    except Exception as e:
        logging.error(f"❌ 统计情报网表写入数量失败: {e}")
        return 0

def count_recent_writes_in_main_table():
    """
    统计最近写入主表的记录数量（最近5分钟内）
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        today = datetime.datetime.now().strftime('%Y-%m-%d')

        # 查询最近5分钟内写入的记录数
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM `{TABLE_NAME}`
            WHERE date = %s
            AND get_date >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        """, (today,))

        recent_count = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        logging.info(f"📊 最近5分钟内写入主表记录数: {recent_count}")
        return recent_count

    except Exception as e:
        logging.error(f"❌ 统计最近写入数量失败: {e}")
        return 0

def count_duplicate_in_main_table():
    """
    统计最近写入主表中标记为重复的文件数量（repeat=1）
    """
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        today = datetime.datetime.now().strftime('%Y-%m-%d')

        # 查询最近5分钟内写入的重复记录数（repeat=1表示在大库中重复）
        cursor.execute(f"""
            SELECT COUNT(*)
            FROM `{TABLE_NAME}`
            WHERE date = %s
            AND get_date >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            AND `repeat` = 1
        """, (today,))

        duplicate_count = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        logging.info(f"📊 主表查重统计: {duplicate_count} 个文件在大库中重复")
        return duplicate_count

    except Exception as e:
        logging.error(f"❌ 统计主表查重失败: {e}")
        return 0

def write_precise_samples_to_qingbao():
    """
    将今日的样本写入情报网数据库（包括精确目录和W站样本）
    """
    try:
        # 直接连接主数据库查询今日的样本，不再创建表
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 查询今日所有样本（包含VT首次提交时间），不限制source
        query_sql = f"""
        SELECT md5, file_name, file_size, src_path, get_date, date, vt_first_submit_time
        FROM `{TABLE_NAME}`
        WHERE date = CURDATE()
        ORDER BY id DESC
        """

        cursor.execute(query_sql)
        samples = cursor.fetchall()
        cursor.close()
        conn.close()

        if not samples:
            logging.info("📝 今日暂无新样本需要写入情报网")
            return 0

        logging.info(f"📊 发现 {len(samples)} 个今日样本需要写入情报网")

        success_count = 0
        for sample in samples:
            md5, filename, file_size, src_path, get_date, date, vt_first_submit_time = sample

            if write_to_qingbao_db(md5, filename, file_size, date, src_path, vt_first_submit_time):
                success_count += 1

        logging.info(f"✅ 成功写入情报网数据库: {success_count}/{len(samples)} 个样本")
        return success_count

    except Exception as e:
        logging.error(f"❌ 写入情报网数据库失败: {e}")
        return 0

def is_executable_file(file_path):
    """
    判断是否为可执行文件（Windows或Linux）
    特殊规则：W站文件夹中的所有文件都判定为可执行
    其他文件：Windows PE文件头MZ或扩展名为脚本类型，Linux ELF文件头
    """
    # 特殊规则：W站文件夹中的所有文件都判定为可执行
    normalized_path = os.path.normpath(file_path).replace('\\', '/')
    # W站文件夹判断：检查路径中是否包含W站文件夹
    path_parts = normalized_path.split('/')
    if 'W站' in path_parts:
        logging.info(f"🎯 W站文件夹文件，直接判定为可执行: {file_path}")
        return True

    script_exts = ['.bat', '.cmd', '.ps1', '.vbs', '.js', '.py', '.sh']
    pe_exts = ['.exe', '.dll', '.sys', '.com', '.scr']
    ext = os.path.splitext(file_path)[1].lower()

    try:
        with open(file_path, 'rb') as f:
            head = f.read(4)

            # Windows PE文件：检查MZ头（无论是否有扩展名）
            if head[:2] == b'MZ':
                return True

            # Linux ELF文件
            if head[:4] == b'\x7fELF':
                return True

            # 脚本文件：通过扩展名判断
            if ext in script_exts:
                return True

            # 其他PE相关扩展名（即使没有MZ头也可能是可执行的）
            if ext in pe_exts:
                return True

    except Exception as e:
        logging.warning(f"无法判断文件类型: {file_path}, 错误: {e}")
    return False

# 新增：写入virus_clean_log表的函数
def write_virus_clean_log(file_name, file_path, clean_result, details_dict):
    try:
        # 将 details_dict 的 key 和内容全部转为中文（去除失败详情）
        details_cn = {
            "解压样本总数": details_dict.get('total_extracted', 0),
            "可执行文件数": details_dict.get('executable_count', 0),
            f"写入{TABLE_NAME}数量": details_dict.get(f'written_{TABLE_NAME}_数量', details_dict.get('written_t_samples_all_bak', 0)),
            "写入users_virussample数量": details_dict.get('written_users_virussample', 0),
            "写入大库数量": details_dict.get('written_t_hash_all', 0),
            "写入小库数量": details_dict.get('written_t_hash_ransom', 0),
            "clean样本数": details_dict.get('clean_samples', 0),
            "not_scan样本数": details_dict.get('not_scan_samples', 0),
            "非可执行文件数": details_dict.get('non_executable_count', 0),
            f"查重{TABLE_NAME}数量": details_dict.get('duplicate_in_main_table', 0)
        }
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        insert_sql = '''
            INSERT INTO users_viruscleanlog (file_name, file_path, clean_result, details, created_at)
            VALUES (%s, %s, %s, %s, %s)
        '''
        details_json = json.dumps(details_cn, ensure_ascii=False)
        now = datetime.datetime.now()
        cursor.execute(insert_sql, (file_name, file_path, clean_result, details_json, now))
        conn.commit()
        cursor.close()
        conn.close()
        logging.info(f"✅ 已写入virus_clean_log日志: {file_name} | 结果: {clean_result}")
    except Exception as e:
        logging.error(f"❌ 写入virus_clean_log失败: {file_name}, 错误: {e}")

if __name__ == '__main__':
    try:
        # 让用户在命令行输入精确监控目录
        precise_dir = input(f"请输入精确监控目录，直接回车用默认 [{PRECISE_WATCH_DIR}]：").strip()
        if precise_dir:
            PRECISE_WATCH_DIR = precise_dir

        # 让用户在命令行输入解压目标目录
        unzip_dir = input(f"请输入解压目标根目录，直接回车用默认 [{UNZIP_BASE_DIR}]：").strip()
        if unzip_dir:
            UNZIP_BASE_DIR = unzip_dir

        print(f"精确监控目录：{PRECISE_WATCH_DIR}")
        print(f"解压目录：{UNZIP_BASE_DIR}")

        main_loop()
    except Exception as e:
        import traceback
        print("程序发生异常：")
        traceback.print_exc()
        input("按回车键退出...") 