#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import tempfile

def test_wstation_logic_fix():
    """测试修复后的W站文件夹判断逻辑"""
    
    # 导入修改后的函数
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import is_executable_file
    
    # 测试用例
    test_cases = [
        # 应该被识别为W站文件的路径
        ("W站/test.txt", True, "W站根目录文件"),
        ("W站/subfolder/file.dat", True, "W站子目录文件"),
        ("some/path/W站/file.unknown", True, "路径中包含W站文件夹"),
        ("extract/W站/sample.data", True, "解压目录中的W站文件夹"),
        
        # 不应该被识别为W站文件的路径
        ("normal/test.txt", False, "普通目录文件"),
        ("data/file.bin", False, "数据目录文件"),
        ("W站相关/file.dat", False, "包含W站字符但不是W站文件夹"),
        ("新W站/file.dat", False, "包含W站字符但不是W站文件夹"),
        ("other/folder/file.dat", False, "其他文件夹")
    ]
    
    print("测试修复后的W站文件夹判断逻辑:")
    print("=" * 70)
    
    # 创建临时目录和文件进行测试
    with tempfile.TemporaryDirectory() as temp_dir:
        for test_path, expected_wstation, description in test_cases:
            # 创建完整路径
            full_path = os.path.join(temp_dir, test_path)
            
            # 创建目录
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            # 创建测试文件（写入一些非可执行内容）
            with open(full_path, 'wb') as f:
                f.write(b'This is test data, not executable')
            
            # 测试判断结果
            result = is_executable_file(full_path)
            
            # 检查结果是否符合预期
            if expected_wstation:
                # W站文件应该返回True
                status = "✅ 正确" if result else "❌ 错误"
                expected_text = "应识别为可执行"
            else:
                # 非W站文件应该返回False（因为不是真正的可执行文件）
                status = "✅ 正确" if not result else "❌ 错误"
                expected_text = "应识别为非可执行"
            
            wstation_mark = "🎯 W站" if expected_wstation else "📁 普通"
            
            print(f"{status} | {wstation_mark} | {result} | {expected_text} | {description}")
            print(f"     路径: {test_path}")
            print()

if __name__ == "__main__":
    test_wstation_logic_fix()
