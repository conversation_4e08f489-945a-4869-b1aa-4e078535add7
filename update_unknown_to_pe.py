#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
将users_virussample表中的"未知文件类型"批量修改为"PE"
"""

import sys
import os

# 添加virus_clean模块路径
sys.path.append('virus_clean')

def update_unknown_to_pe():
    """将未知文件类型修改为PE"""
    print("🔄 将未知文件类型批量修改为PE")
    print("=" * 60)
    
    try:
        from auto_unzip_and_md5 import DB_CONFIG, QINGBAO_TABLE
        import pymysql
        
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 1. 查询当前未知文件类型的记录数
        print("1. 查询当前状态:")
        print("-" * 30)
        
        cursor.execute(f"""
        SELECT COUNT(*) as count
        FROM `{QINGBAO_TABLE}`
        WHERE file_type = '未知文件类型'
        """)
        
        unknown_count = cursor.fetchone()[0]
        print(f"   当前'未知文件类型'记录数: {unknown_count}")
        
        if unknown_count == 0:
            print("   ✅ 没有需要更新的记录")
            cursor.close()
            conn.close()
            return
        
        # 显示一些示例记录
        cursor.execute(f"""
        SELECT id, file_name, file_type
        FROM `{QINGBAO_TABLE}`
        WHERE file_type = '未知文件类型'
        ORDER BY id DESC
        LIMIT 5
        """)
        
        sample_records = cursor.fetchall()
        print("   示例记录:")
        for record in sample_records:
            record_id, file_name, file_type = record
            print(f"      ID:{record_id} | {file_name[:30]:<30} | {file_type}")
        
        print()
        
        # 2. 执行批量更新
        print("2. 执行批量更新:")
        print("-" * 30)
        
        update_sql = f"""
        UPDATE `{QINGBAO_TABLE}` 
        SET file_type = 'PE', updated_at = NOW()
        WHERE file_type = '未知文件类型'
        """
        
        print(f"   执行SQL: {update_sql}")
        print(f"   即将更新 {unknown_count} 条记录...")
        
        # 确认操作
        confirm = input("   确认执行更新? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("   操作已取消")
            cursor.close()
            conn.close()
            return
        
        # 执行更新
        cursor.execute(update_sql)
        affected_rows = cursor.rowcount
        conn.commit()
        
        print(f"   ✅ 更新完成，影响行数: {affected_rows}")
        
        print()
        
        # 3. 验证更新结果
        print("3. 验证更新结果:")
        print("-" * 30)
        
        # 检查是否还有未知文件类型
        cursor.execute(f"""
        SELECT COUNT(*) as count
        FROM `{QINGBAO_TABLE}`
        WHERE file_type = '未知文件类型'
        """)
        
        remaining_unknown = cursor.fetchone()[0]
        print(f"   剩余'未知文件类型'记录数: {remaining_unknown}")
        
        # 检查PE类型记录数
        cursor.execute(f"""
        SELECT COUNT(*) as count
        FROM `{QINGBAO_TABLE}`
        WHERE file_type = 'PE'
        """)
        
        pe_count = cursor.fetchone()[0]
        print(f"   当前'PE'类型记录数: {pe_count}")
        
        # 显示最近更新的记录
        cursor.execute(f"""
        SELECT id, file_name, file_type, updated_at
        FROM `{QINGBAO_TABLE}`
        WHERE file_type = 'PE' 
        AND updated_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ORDER BY updated_at DESC
        LIMIT 5
        """)
        
        recent_updates = cursor.fetchall()
        if recent_updates:
            print("   最近更新的记录:")
            for record in recent_updates:
                record_id, file_name, file_type, updated_at = record
                print(f"      ID:{record_id} | {file_name[:30]:<30} | {file_type} | {updated_at}")
        
        print()
        
        # 4. 统计所有文件类型分布
        print("4. 当前文件类型分布:")
        print("-" * 30)
        
        cursor.execute(f"""
        SELECT file_type, COUNT(*) as count
        FROM `{QINGBAO_TABLE}`
        WHERE file_type IS NOT NULL AND file_type != ''
        GROUP BY file_type
        ORDER BY count DESC
        LIMIT 10
        """)
        
        file_type_stats = cursor.fetchall()
        for file_type, count in file_type_stats:
            print(f"   {file_type:<20}: {count} 个")
        
        cursor.close()
        conn.close()
        
        print()
        print("=" * 60)
        print("🎯 批量更新完成!")
        print(f"✅ 成功将 {affected_rows} 条'未知文件类型'记录更新为'PE'")
        
        if remaining_unknown == 0:
            print("✅ 所有'未知文件类型'记录已清理完毕")
        else:
            print(f"⚠️  仍有 {remaining_unknown} 条'未知文件类型'记录")
        
        print()
        print("💡 说明:")
        print("- PE (Portable Executable) 是Windows可执行文件格式")
        print("- 适用于大部分Windows恶意软件样本")
        print("- 如需更精确的文件类型，建议使用VT批量更新脚本")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("⚠️  即将将users_virussample表中的'未知文件类型'批量修改为'PE'")
    print("   这是一个批量操作，请确认后执行")
    print()
    
    update_unknown_to_pe()
