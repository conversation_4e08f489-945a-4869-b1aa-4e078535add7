#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import datetime

def check_duplicate_files():
    """检查最近处理的文件是否都是重复的"""
    
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import DB_CONFIG, TABLE_NAME
    import pymysql
    
    print("🔍 检查文件重复情况:")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        
        # 1. 检查今日所有记录的处理时间分布
        print("1. 今日记录的时间分布:")
        cursor.execute(f"""
            SELECT DATE_FORMAT(get_date, '%%H:%%i:%%s') as time, COUNT(*) as count
            FROM `{TABLE_NAME}`
            WHERE date = %s
            GROUP BY DATE_FORMAT(get_date, '%%H:%%i:%%s')
            ORDER BY get_date DESC
            LIMIT 10
        """, (today,))
        time_groups = cursor.fetchall()
        
        for time_str, count in time_groups:
            print(f"   {time_str}: {count} 条记录")
        
        print()
        
        # 2. 检查最近1小时的记录详情
        print("2. 最近1小时的记录详情:")
        cursor.execute(f"""
            SELECT md5, file_name, get_date, source, `repeat`
            FROM `{TABLE_NAME}` 
            WHERE date = %s AND get_date >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY get_date DESC
            LIMIT 20
        """, (today,))
        recent_records = cursor.fetchall()
        
        if recent_records:
            print(f"   找到 {len(recent_records)} 条最近记录:")
            repeat_count = 0
            new_count = 0
            
            for i, record in enumerate(recent_records, 1):
                md5, file_name, get_date, source, repeat = record
                status = "重复" if repeat == 1 else "新增"
                if repeat == 1:
                    repeat_count += 1
                else:
                    new_count += 1
                
                print(f"   {i:2d}. {status} | {file_name[:25]:<25} | {get_date} | {source}")
            
            print(f"\n   统计: 新增={new_count}, 重复={repeat_count}")
            
            if repeat_count > 0:
                print(f"   ⚠️  发现 {repeat_count} 条重复记录（大库重复）")
        else:
            print("   最近1小时没有记录")
        
        print()
        
        # 3. 检查是否有完全相同的MD5被多次写入
        print("3. 检查MD5重复写入情况:")
        cursor.execute(f"""
            SELECT md5, COUNT(*) as count, GROUP_CONCAT(get_date ORDER BY get_date DESC) as times
            FROM `{TABLE_NAME}` 
            WHERE date = %s 
            GROUP BY md5 
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            LIMIT 10
        """, (today,))
        duplicate_md5s = cursor.fetchall()
        
        if duplicate_md5s:
            print(f"   发现 {len(duplicate_md5s)} 个MD5被多次写入:")
            for md5, count, times in duplicate_md5s:
                print(f"   MD5: {md5[:16]}... | 写入次数: {count}")
                print(f"        时间: {times}")
        else:
            print("   ✅ 没有发现MD5重复写入")
        
        print()
        
        # 4. 模拟你最近一次处理的情况
        print("4. 模拟最近一次处理:")
        
        # 获取最近的一批记录（可能是同一次处理的）
        cursor.execute(f"""
            SELECT md5, file_name, get_date
            FROM `{TABLE_NAME}` 
            WHERE date = %s 
            ORDER BY get_date DESC
            LIMIT 20
        """, (today,))
        latest_batch = cursor.fetchall()
        
        if latest_batch:
            latest_time = latest_batch[0][2]
            print(f"   最新处理时间: {latest_time}")
            
            # 检查这批文件如果再次处理会怎样
            print("   如果再次处理这些文件:")
            would_skip = 0
            would_process = 0
            
            for md5, file_name, get_date in latest_batch:
                # 检查MD5是否已存在
                cursor.execute(f"SELECT COUNT(*) FROM `{TABLE_NAME}` WHERE md5=%s", (md5,))
                exists = cursor.fetchone()[0] > 0
                
                if exists:
                    would_skip += 1
                else:
                    would_process += 1
            
            print(f"   会跳过: {would_skip} 个文件（MD5已存在）")
            print(f"   会处理: {would_process} 个文件（新MD5）")
            print(f"   预期写入数量: {would_process}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    print("=" * 60)
    print("🎯 结论:")
    print("如果你看到'写入数量=0'，很可能是因为:")
    print("1. 这些文件之前已经处理过了")
    print("2. 同一个压缩包被多次处理")
    print("3. 文件的MD5在数据库中已经存在")
    print()
    print("💡 建议:")
    print("1. 尝试处理一个全新的压缩包")
    print("2. 或者清空今日的测试数据重新测试")

if __name__ == "__main__":
    check_duplicate_files()
