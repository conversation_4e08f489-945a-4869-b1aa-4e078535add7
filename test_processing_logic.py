#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import tempfile

def test_processing_logic():
    """测试文件处理逻辑：可执行文件判断 vs MD5查重"""
    
    # 导入函数
    sys.path.append('virus_clean')
    from auto_unzip_and_md5 import is_executable_file, calc_md5
    
    print("测试文件处理逻辑:")
    print("=" * 60)
    
    # 创建测试文件
    with tempfile.TemporaryDirectory() as temp_dir:
        # 测试文件列表
        test_files = [
            # (文件名, 文件内容, 预期是否可执行)
            ("pe_file.exe", b'MZ\x90\x00\x03\x00\x00\x00\x04\x00\x00\x00\xff\xff\x00\x00', True),
            ("script_file.bat", b'echo hello world', True),
            ("text_file.txt", b'This is a text file', False),
            ("data_file.bin", b'\x00\x01\x02\x03\x04\x05', False),
            ("w_station_file", b'some data in W station', False),  # 将放在W站文件夹中
        ]
        
        print("1. 测试可执行文件判断:")
        print("-" * 40)
        
        for filename, content, expected_executable in test_files:
            # 创建普通文件
            file_path = os.path.join(temp_dir, filename)
            with open(file_path, 'wb') as f:
                f.write(content)
            
            # 测试可执行文件判断
            is_exec = is_executable_file(file_path)
            status = "✅" if is_exec == expected_executable else "❌"
            
            print(f"{status} {filename}: 判断={is_exec}, 预期={expected_executable}")
            
            # 计算MD5
            md5 = calc_md5(file_path)
            print(f"   MD5: {md5}")
            print()
        
        print("2. 测试W站文件夹特殊逻辑:")
        print("-" * 40)
        
        # 创建W站文件夹
        w_station_dir = os.path.join(temp_dir, "W站")
        os.makedirs(w_station_dir, exist_ok=True)
        
        # 在W站文件夹中创建文件
        w_file_path = os.path.join(w_station_dir, "w_station_file")
        with open(w_file_path, 'wb') as f:
            f.write(b'some data in W station')
        
        is_exec = is_executable_file(w_file_path)
        print(f"W站文件夹中的文件: {is_exec} (应该是True)")
        
        print()
        print("3. 处理逻辑总结:")
        print("-" * 40)
        print("文件处理流程:")
        print("1. 首先判断是否为可执行文件 (is_executable_file)")
        print("2. 如果不是可执行文件 -> 跳过，记录[SKIP]日志")
        print("3. 如果是可执行文件 -> 计算MD5")
        print("4. 调用write_to_db进行MD5查重")
        print("5. 如果MD5重复 -> 跳过写入，记录'MD5已存在'日志")
        print("6. 如果MD5不重复 -> 写入数据库")
        print()
        print("结论:")
        print("- [SKIP]日志 = 文件被判断为非可执行文件")
        print("- 'MD5已存在'日志 = 文件是可执行的，但MD5重复")
        print("- 这两种情况不会相互影响")

if __name__ == "__main__":
    test_processing_logic()
